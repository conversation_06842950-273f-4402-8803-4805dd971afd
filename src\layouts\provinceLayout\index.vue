<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-11-07 11:17:21
 * @LastEditors: wjb
 * @LastEditTime: 2023-08-25 08:59:01
-->
<template>
  <div class="wrapper" id="wrapper">
    <div style="width: 100%;height:93px;">
      <topTitle :title="titleName"></topTitle>
    </div>
    <router-view />
  </div>
</template>

<script>
import topTitle from '@/components/TopTitle/index.vue'
import {} from '@/api/common'
export default {
  name: 'ProvinceLayout',
  components: {
    topTitle,
  },
  data() {
    return {
      //标题名称
      titleName: '金华市统一运维平台',
    }
  },
  mounted() {},
  watch: {},
  methods: {
    clickLi(item, i) {
      // if (item.name == '除险保安') {
      //   window.open('http://***************:85/cxba/#/login')
      // } else {
      this.activeIndex = i
      this.$router.push({
        path: item.url,
      })
      // }
    },
  },
}
</script>

<style lang="less" scoped>
.wrapper {
  background-image: url('@/assets/img/common/bg.png');
  width: 100%;
  height: 100%;
  background-size: cover;
}
</style>
