import { request } from '@/utils/request'

// 查询事件基本信息列表
export const getJbxx = (params) => {
  return request(`/kshsj/jbxx`, 'get', params)
}

// 中间区域（告警相关）
export const getGjxg = (params) => {
  return request(`/kshsj/gjxg`, 'get', params)
}

// 告警应用top5
export const getGjyyTop5 = (params) => {
  return request(`/kshsj/gjyyTop5`, 'get', params)
}

// 隐患告警情况
export const getYhgjqk = (params) => {
  return request(`/kshsj/yhgjqk`, 'get', params)
}

// 告警等级统计
export const getGjjjcdfx = (params) => {
  return request(`/gjzl/gjjjcdfx`, 'get', params)
}

// 单位列表
export const getDwList = (params) => {
  return request(`/kshsj/dwList`, 'get', params)
}

// 应用监控列表
export const getYyJcList = (params) => {
  return request(`/kshsj/yyJcList`, 'get', params)
}

// 云资源预警列表(电信)
export const getYzyyjList = (params) => {
  return request(`/kshsj/yzyyjList`, 'get', params)
}

// 安全隐患列表
export const getAqyhList = (params) => {
  return request(`/kshsj/aqyhList`, 'get', params)
}

// 数据库预警列表
export const getSjkyjList = (params) => {
  return request(`/kshsj/sjkyjList`, 'get', params)
}

// 移动告警列表
export const getYdgjList = (params) => {
  return request(`/kshsj/ydgjList`, 'get', params)
}