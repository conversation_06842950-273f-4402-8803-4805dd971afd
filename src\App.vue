<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-11-07 10:55:13
 * @LastEditors: wjb
 * @LastEditTime: 2024-12-17 15:47:05
-->
<template>
  <div id="app">
    <div
      class="scale-wrapper"
      :style="`width: ${$pageWidth}px; height: ${$pageHeight}px; transform: scale(${scaleWidth}, ${scaleHeight})`"
    >
      <router-view></router-view>
    </div>
  </div>
</template>

<script>

export default {
  name: 'App',
  data() {
    return {
      scaleWidth: 1,
      scaleHeight: 1,
      $pageWidth: 1920,
      $pageHeight: 1080,
    }
  },

  watch: {
    '$route.path': function() {
        this.changePage();

      },
      '$pageWidth': function() {
        this.changePage();
      }
  },
  created() {
    this.changePage();
  },
  mounted() {
    this.$nextTick(() => {
      this.resetPageSize();
      window.addEventListener('resize', this.resetPageSize);
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resetPageSize);
  },
  methods: {
    changePage() {
      this.resetPageSize();
    },
    resetPageSize() {
      const container = document.getElementById('app');
      this.scaleWidth = container.clientWidth / this.$pageWidth;
      this.scaleHeight = container.clientHeight / this.$pageHeight;
    }
  }
}
</script>

<style>
* {
  padding: 0;
  margin: 0;
}
li{
  list-style: none;
}
#app {
  height: 100%;
}
.scale-wrapper {
  transform-origin: left top;
  height: 100%;
  width: 100%;
}
</style>
