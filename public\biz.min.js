/*
 * @Author: xk_<PERSON><PERSON>
 * @Date: 2022-10-11 16:43:07
 * @E-mail: <EMAIL>
 * @LastEditors: Wang_Jin<PERSON>o && <EMAIL>
 * @LastEditTime: 2023-06-15 11:13:08
 * @Desc:
 */
var mapUtil = {
    events: {},
    popups: {},
    mapclick: null,
    layerstate: {},
    _weatherSwitch: true,
    _baseMapType: "black",
    _clickEvts: {},
    _user: "main",//功能使用模块
    _clickEvtPoint: "",
    _postData(params) {
        // this._setMap()
    },
    _getKeyValue(obj, dict) {
        let keys = [], values = []
        obj && Object.keys(dict).forEach(key => {
            keys.push(dict[key] || "--")
            values.push(obj[key] || "--")
        })
        return { key: keys, value: values }
    },
    _ajaxQuery: function (url, data, successfn, errorfn) {
        data = (data == null || data == "" || typeof (data) == "undefined") ? {
            "date": new Date().getTime()
        }
            : data;
        $.ajax({
            type: "get",
            data: data,
            url: url,
            async: true,
            dataType: "json",
            traditional: true,
            success: function (d) {
                successfn(d);
            },
            error: function (e) {
                if (errorfn) errorfn(e);
            }
        });
    },
    _checkBeforeLoad(layerid) {
        if (!layerid) {
            console.error('layerid不可为空')
            return false
        }
        if (this.layers[layerid]) {
            console.error('图层 ', layerid, ' 已存在')
            return false
        }
        return true
    },
    _sortClickEvts() {
        // 事件排序
        let pointlyr = [], polygonlyr = []
        for (let i = 0; i < ArcGisUtils.mapClickEventHandle._callbackEvent.length; i++) {
            const __id = ArcGisUtils.mapClickEventHandle._callbackEvent[i].layerId
            if (view.map.findLayerById(__id)) {
                if (view.map.findLayerById(__id)?.geometryType == "polygon") polygonlyr.push(ArcGisUtils.mapClickEventHandle._callbackEvent[i])
                else {
                    pointlyr.push(ArcGisUtils.mapClickEventHandle._callbackEvent[i])
                }
            }
        }
        ArcGisUtils.mapClickEventHandle._callbackEvent = [...pointlyr, ...polygonlyr]
    },
    loadPointLayer(opts) {
        let datacfg = opts.datacfg || {};
        let iconcfg = opts.iconcfg || { image: "" };
        console.log(iconcfg)
        let labelcfg = opts.labelcfg || {};
        let layercfg = opts.layercfg || {};
        let popcfg = opts.popcfg || {}
        let onclick = opts.onclick
        let layerid = opts.layerid || layercfg.layerid
        let data = opts.data
        let iconlist = iconcfg.iconlist || {}
        const { cluster = true, viewer = this.mapview } = opts
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layercfg.layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _data = []
        const _attr = {}
        data.forEach((item, idx) => {
            item.layerid = layerid
            item.guid = this.guid()
            item.objid = idx
            let lng = item[datacfg.lngfield] * 1 || item.lng * 1;
            let lat = item[datacfg.latfield] * 1 || item.lat * 1;
            if (lng != '' &&
                lng != 0.0 &&
                lng != null &&
                lng != undefined &&
                !isNaN(lng)) {
                item.code = 3
                item.longitude = lng
                item.latitude = lat
                item.esX = lng
                item.esY = lat
                _data.push(item)
                _attr[item.guid] = item.data
            }
            if (!item.name) item.name = item.guid   //聚合功能data必传name字段
            if (cluster) item.id = item.guid       //聚合功能data必传id字段
        })
        const imgUrl = (iconcfg.image.indexOf(".png") >= 0 || iconcfg.image.indexOf(".gif") >= 0) ? iconcfg.image : null
        if (data.length > 1 && cluster) {//如果cluster为true则进行聚合，默认采用聚合
            let uniqueCfg = { valueArr: [], imgArr: [], sizeArr: [] }
            if (iconlist.list && iconlist.field) {
                iconlist.list.forEach(item => {
                    uniqueCfg.valueArr.push(item.value)
                    uniqueCfg.imgArr.push(item.src)
                    uniqueCfg.sizeArr.push(item.size)
                })
                uniqueCfg.field = iconlist.field
            }
            this.loadClusterLayer({
                layerid: layerid,
                data: data,
                lyrCfg: {
                    field: "id", // 接口返回值：唯一的字段
                    clusterImg: imgUrl, // 聚合图标地址
                    iconImg: imgUrl, // 默认图标地址
                    criticalZoom: 17,
                },
                popCfg: {
                    title: popcfg.title,//标题
                    dict: popcfg.dict,
                    attr: _attr,
                    onclick
                },
                uniqueCfg,
              console.log(uniqueCfg)
            });
            return;
        }
        let rendererIcon = {
            size: iconcfg.size || 64, // 图片大小
            src: imgUrl, // 图片src
        }
        console.log(rendererIcon)
        if (iconlist) {
            rendererIcon.field = iconlist.field
            rendererIcon.uniqueValueInfos = iconlist.list
            // 解决数据只有一条的时候，uniqueValueInfos不起作用的问题，mod 2023年5月11日
            if (data.length == 1 && iconlist.field && iconlist.list && data[0][iconlist.field] && iconlist.list[data[0][iconlist.field]]) {
                rendererIcon.src = iconlist.list[data[0][iconlist.field]]
            }
        }
        debugger
        gis.loadArcgisLayer(viewer, {
            code: 3,
            data: _data,
            type: "customFeature",
            objectIdField: "objid",
            rendererIcon,
        }).then(res => {
            const pointEventId = ArcGisUtils.mapClickEventHandle.add(res.id, (point, graphic, graphics) => {
                const pointStr = `${point.x},${point.y}`
                if (mapUtil._clickEvtPoint == pointStr) return
                mapUtil._clickEvtPoint = pointStr
                if (graphic && graphics.length == 1) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (onclick) onclick(graphic.attributes, graArray)
                    if (popcfg.dict) {
                        this._createPopup({
                            layerid,
                            position: graphic.geometry,
                            dict: popcfg.dict,
                            attr: graphic.attributes,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }

                } else if (graphics.length > 1) {
                    const datas = [];
                    for (let i = 0; i < graphics.length; i++) {
                        const { attributes } = graphics[i];
                        attributes.data = _attr[attributes.guid]
                        datas.push(attributes)
                    }
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    if (onclick) {
                        let graArray = []
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                        onclick(graphic.attributes, graArray)
                    } else {
                        this._createPopup({
                            layerid,
                            position: point,
                            dict: popcfg.dict,
                            attr: datas,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }
                }
            })
            this._sortClickEvts()
            setTimeout(() => {
                $('.mapPopup').css({
                    'background': 'none', 'box-shadow': 'none'
                })
                $('.mapPopup .header').css({ display: 'none' })
                $('.mapPopup div.body::before').css({ display: 'none' })
            }, 450);
            //鼠标滑动事件
            if (opts.onblur && !this.blurevts[layerid]) {
                this.blurevts[layerid] = function (e, pt) {
                    if (layerid == e.layerid)
                        opts.onblur({ ...e, data: _attr[e.guid], position: pt })
                }
            }
            this.layers[layerid] = res
            if (opts.onload) opts.onload(res)
        })
    },
    loadPointLayer1(opts) {
        let datacfg = opts.datacfg || {};
        let iconcfg = opts.iconcfg || { image: "" };
        console.log(iconcfg)
        let labelcfg = opts.labelcfg || {};
        let layercfg = opts.layercfg || {};
        let popcfg = opts.popcfg || {}
        let onclick = opts.onclick
        let layerid = opts.layerid || layercfg.layerid
        let data = opts.data
        let iconlist = iconcfg.iconlist || {}
        const { cluster = true, viewer = this.mapview } = opts
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layercfg.layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _data = []
        const _attr = {}
        data.forEach((item, idx) => {
            item.layerid = layerid
            item.guid = this.guid()
            item.objid = idx
            let lng = item[datacfg.lngfield] * 1 || item.lng * 1;
            let lat = item[datacfg.latfield] * 1 || item.lat * 1;
            if (lng != '' &&
                lng != 0.0 &&
                lng != null &&
                lng != undefined &&
                !isNaN(lng)) {
                item.code = 3
                item.longitude = lng
                item.latitude = lat
                item.esX = lng
                item.esY = lat
                _data.push(item)
                _attr[item.guid] = item.data
            }
            if (!item.name) item.name = item.guid   //聚合功能data必传name字段
            if (cluster) item.id = item.guid       //聚合功能data必传id字段
        })
        const imgUrl = (iconcfg.image.indexOf(".png") >= 0 || iconcfg.image.indexOf(".gif") >= 0) ? iconcfg.image : null
        if (data.length > 1 && cluster) {//如果cluster为true则进行聚合，默认采用聚合
            let uniqueCfg = { valueArr: [], imgArr: [], sizeArr: [] }
            if (iconlist.list && iconlist.field) {
                iconlist.list.forEach(item => {
                    uniqueCfg.valueArr.push(item.value)
                    uniqueCfg.imgArr.push(item.src)
                    uniqueCfg.sizeArr.push(item.size)
                })
                uniqueCfg.field = iconlist.field
            }
            this.loadClusterLayer({
                layerid: layerid,
                data: data,
                lyrCfg: {
                    field: "id", // 接口返回值：唯一的字段
                    clusterImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png` || '', // 聚合图标地址
                    iconImg: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 默认图标地址
                    criticalZoom: 17,
                },
                popCfg: {
                    title: popcfg.title,//标题
                    dict: popcfg.dict,
                    attr: _attr,
                    onclick
                },
                uniqueCfg,
            });
            return;
        }
        let rendererIcon = {
            size: iconcfg.iconSize || 64, // 图片大小
            src: imgUrl || `https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${iconcfg.image || "bus"}.png`, // 图片src
        }
        console.log(rendererIcon)
        if (iconlist) {
            rendererIcon.field = iconlist.field
            rendererIcon.uniqueValueInfos = iconlist.list
            // 解决数据只有一条的时候，uniqueValueInfos不起作用的问题，mod 2023年5月11日
            if (data.length == 1 && iconlist.field && iconlist.list && data[0][iconlist.field] && iconlist.list[data[0][iconlist.field]]) {
                rendererIcon.src = iconlist.list[data[0][iconlist.field]]
            }
        }
        gis.loadArcgisLayer(viewer, {
            code: 3,
            data: _data,
            type: "customFeature",
            objectIdField: "objid",
            rendererIcon,
        }).then(res => {
            const pointEventId = ArcGisUtils.mapClickEventHandle.add(res.id, (point, graphic, graphics) => {
                const pointStr = `${point.x},${point.y}`
                if (mapUtil._clickEvtPoint == pointStr) return
                mapUtil._clickEvtPoint = pointStr
                if (graphic && graphics.length == 1) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (onclick) onclick(graphic.attributes, graArray)
                    if (popcfg.dict) {
                        this._createPopup({
                            layerid,
                            position: graphic.geometry,
                            dict: popcfg.dict,
                            attr: graphic.attributes,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }

                } else if (graphics.length > 1) {
                    const datas = [];
                    for (let i = 0; i < graphics.length; i++) {
                        const { attributes } = graphics[i];
                        attributes.data = _attr[attributes.guid]
                        datas.push(attributes)
                    }
                    graphic.attributes.data = _attr[graphic.attributes.guid]
                    if (onclick) {
                        let graArray = []
                        graphics.forEach(item => {
                            item.attributes.data = _attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                        onclick(graphic.attributes, graArray)
                    } else {
                        this._createPopup({
                            layerid,
                            position: point,
                            dict: popcfg.dict,
                            attr: datas,
                            title: popcfg.title || "详情",
                            offset: popcfg.offset,
                        })
                    }
                }
            })
            this._sortClickEvts()
            setTimeout(() => {
                $('.mapPopup').css({
                    'background': 'none', 'box-shadow': 'none'
                })
                $('.mapPopup .header').css({ display: 'none' })
                $('.mapPopup div.body::before').css({ display: 'none' })
            }, 450);
            //鼠标滑动事件
            if (opts.onblur && !this.blurevts[layerid]) {
                this.blurevts[layerid] = function (e, pt) {
                    if (layerid == e.layerid)
                        opts.onblur({ ...e, data: _attr[e.guid], position: pt })
                }
            }
            this.layers[layerid] = res
            if (opts.onload) opts.onload(res)
        })
    },
    _createPopup(params) {
        if (gis.mapPopupWidget._popupRef) gis.mapPopupWidget.close()
        const layerid = params.layerid
        const position = {
            "spatialReference": {
                "wkid": 4490
            },
            "x": params.position[0] * 1 || params.position.x,
            "y": params.position[1] * 1 || params.position.y,
            "z": 10,
            "longitude": params.position.x || params.position[0] * 1,
            "latitude": params.position.y || params.position[1] * 1,
        }
        let popup = {
            point: position,
            title: params.title || '属性',
            offset: params.offset || [0, 0],
            onClose: () => {
                this.logInfo("关闭弹窗");
            },
            width: params.width,
        }
        let div = document.createElement('div')
        if (params.content) {
            div.innerHTML = params.content
            popup.content = div
        }
        else if (params.dict) {
            let attr = params.attr;
            if (Array.isArray(attr) && attr.length > 1) {//根据attr类型以及长度判断是否需要分页，若需要分页则使用默认样式，并将数据结构变更为：[[{}]]
                const datas = [];
                for (let i = 0; i < attr.length; i++) {
                    const data = [];
                    Object.keys(params.dict).forEach(item => {//根据dict中指定字段赋值
                        data.push({ key: params.dict[item], value: attr[i][item] });
                    })
                    datas.push(data)
                }
                popup.data = datas    //使用默认弹窗样式时传data:[[{key:'',value:''}]]
                setTimeout(() => {
                    $('.mapPopup').css({
                        'background': 'rgba(14, 44, 94, 0.8)', 'box-shadow': 'inset 0px 0px 16px 0px rgba(54, 162, 237, 0.62)'
                    })
                    $('.mapPopup .bodyContent').css({
                        'z-index': '1', 'padding': '20px',
                    })
                    $('.mapPopup .header').css({ display: 'flex' })
                }, 400);
            } else {
                popup.offset = [65, -40]
                let rowlist = ""
                Object.keys(params.dict).forEach(item => {
                    rowlist += `<div style="color: #ffff; left: 30px;"><span>${params.dict[item]}：</span><span>${attr[item] ? attr[item] : attr[0][item]}</span></div>`//attr[item]用于非聚合图层，attr[0][item]用于聚合图层单点
                })
                let _content = `
                    <div
                          style="
                            position: relative;
                            background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                            background-size: 100% 100%;
                            width: max-content;
                            min-height: 250px;
                          "
                        >
                          <nav
                            style="
                              display: flex;
                              justify-content: space-between;
                              align-items: flex-end;
                              padding-bottom: 10px;
                              margin: 0 20px;
                              border-bottom: 1px solid;
                              border-bottom: 2px solid;
                              border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                              padding-left: 20px;
                            "
                          >
                            <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">${params.title}</h2>
                            <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none'">
                                <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
                            </span>
                          </nav>

                          <header
                            style="
                              padding-bottom: 15%;
                              margin: 10px 20px 0;
                              display: flex;
                              justify-content: space-between;
                              font-size: 25px;
                            "
                          >
                            <div style="margin-left: 40px">
                              <p style="width: 650px;font-size: 32px;color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
                              ${rowlist}
                              </p>
                            </div>
                          </header>
                </div>`;
                div.innerHTML = _content
                if (rowlist == "") return
                popup.content = div
                setTimeout(() => {
                    $('.mapPopup').css({
                        'background': 'none', 'box-shadow': 'none'
                    })
                    $('.mapPopup .header').css({ display: 'none' })
                    $('.mapPopup div.body::before').css({ display: 'none' })
                }, 450);
            }
        }
        // else if (!params.dict) {
        //     let attr = params.attr;
        //     const datas = [];
        //     for (let i = 0; i < attr.length; i++) {
        //         // 点击到图形
        //         const data = [];
        //         for (key in attr[i]) {
        //             data.push({ key, value: attr[i][key] });
        //         }
        //         datas.push(data)
        //     }
        //     popup.data = datas;
        //     setTimeout(() => {
        //         $('.mapPopup').css({
        //             'background': 'rgba(14, 44, 94, 0.8)', 'box-shadow': 'inset 0px 0px 16px 0px rgba(54, 162, 237, 0.62)'
        //         })
        //         $('.mapPopup .bodyContent').css({
        //             'z-index': '1', 'padding': '20px',
        //         })
        //         $('.mapPopup .header').css({ display: 'flex' })
        //     }, 400);
        // }

        // 适配地图平台的定时器问题
        setTimeout(() => {
            gis.mapPopupWidget.showAt(popup)
            this.popups.layerid = layerid
        }, 400);
    },
    loadAnimatePointLayer(params) {
        let layerid = params.layerid
        let iconcfg = params.iconcfg || {}
        let datacfg = params.datacfg || {}
        let onclick = params.onclick
        let size = iconcfg.size || [100, 100]
        let data = params.data || []
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        let list = []
        data.forEach((item, idx) => {
            item.layerid = layerid
            list.push({
                id: `${layerid}_${idx}`,
                position: [item[datacfg["lngfield"] || "lng"], item[datacfg["latfield"] || "lat"], 100],
                // properties: item,
                // content: `<img src=${iconcfg.image || "http://114.67.84.236:9090/maphp/rkyj.gif"}>`,
                // size: size,
                // offset: [-size[0] / 2, -size[1]],
                radius: size[0] || 120,
                height: size[1] || 180
            })
        })
        const _onclick = function (e) {
            if (onclick) onclick(e.data)
        }
        let layer = gis.addPyramidLayer({ view: this.mapview, data: list }) || {}
        layer.remove = () => {
            gis.removePyramidLayer(this.mapview)
        }
        this.layers[layerid] = layer
    },
    loadPolylineLayer(params) {
        let { layerid, lines, geojson, style } = params
        if (!this._checkBeforeLoad(layerid)) return
        let data = []
        if (lines) {
            geojson = {
                "type": "FeatureCollection",
                "features": []
            }
            lines.forEach((item, idx) => {
                let line = {
                    "type": "Feature",
                    "properties": {},
                    "geometry": {
                        "coordinates": [item],
                        "type": "LineString"
                    }
                }
                geojson.features.push(line)
            })
        }
        geojson.features.forEach(item => {
            data.push({
                paths: item.geometry.coordinates,
                attributes: item.properties
            })
        })
        /* let layer = gis.addLineLayer({
            view,
            data,
            width: style.width || 5,
            color: style.color || "#00ff00"
        })
        this.layers[layerid] = layer */
        const layer = this._createPolyline({
            id: layerid,
            data,
            geojson: geojson,
            style
        })
        this.layers[layerid] = layer
    },
    loadPolylineLayer1(params) {
        let { layerid, data, geojson, style } = params
        if (!this._checkBeforeLoad(layerid)) return
        const layer = this._createPolyline({
            id: layerid,
            data,
            geojson: geojson,
            style
        })
        this.layers[layerid] = layer
    },
    _createPolyline(params) {
        const { id, geojson, data, style = {} } = params
        const color = style.color || [255, 255, 255, 1] //rgba
        const width = style.width || 5 //线宽
        const { isAddArrow = false, isArrowReverse = true } = style
        let layer;
        async function insertPoints(olddata) {
            for (let i = 0; i < olddata.length; i++) {
                olddata[i].paths = await gis.converPathToPoints(olddata[i].paths)
            }
        }
        async function add() {
            await insertPoints(data)
            layer = await gis.addArrowLineLayer({
                view,
                data,
                width,
                color,
            });
            mapUtil.layers[id] = layer
        }
        if (style.image) { //箭头线
            add()
        } else {//正常线
            layer = gis.addLineLayer({
                view,
                data,
                width: style.width || 5,
                color: style.color || "#00ff00",
                isAddArrow,
                isArrowReverse,
            })
            return layer
        }
    },
    loadPolygonLayer(params) {
        let { layerid, data, style = { fillColor: [255, 50, 40, 0.1] }, onclick, zoomToLayer = true, type } = params;
        if (!data) {
            console.error('上图数据不可为空！')
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        let layer, walls = [];
        // 墙体
        if ("wall" == type) {
            // let json = {
            //     "type": "FeatureCollection",
            //     "features": [{
            //         "type": "Feature",
            //         "geometry": {
            //             "type": "Polygon",
            //             "coordinates": [data]
            //         },
            //         "properties": {}
            //     }]
            // }
            data.features.forEach(item => {
                this._createWall({
                    points: item.geometry.coordinates[0],
                    height: 80 || params.height,
                })
            })
        }
        //常规多边形
        let renderer = {
            type: "simple",  // autocasts as new SimpleRenderer()
            symbol: {
                type: "simple-fill",  // autocasts as new SimpleFillSymbol()
                color: style.fillColor || [255, 50, 40, 0.1],
                style: 'solid',
                outline: {  // autocasts as new SimpleLineSymbol()
                    width: style.strokeWidth || 3,
                    color: style.strokeColor || [193, 210, 240, 0.7],
                }
            }
        };
        let _attr_lower = {}
        let _attr_origin = {}
        data.features.forEach((item, idx) => {
            let guid = this.guid()
            item.properties.guid = guid
            let newprop = {
                guid: guid,
                oid_: item.properties.OID
            }
            for (let key in item.properties) {
                newprop[key.toLowerCase()] = item.properties[key]
            }
            _attr_lower[guid] = newprop
            _attr_origin[guid] = item.properties
        })
        // layer = gis.addGeojsonToMap(mapUtil.mapview, data, { renderer });
        layer = gisHelper.createPolygonLayer({ data, view, style })
        this.layers[layerid] = layer;
        if ("wall" == type) {
            this.layers[layerid] = {
                remove: () => {
                    this.mapview.map.remove(layer)
                    gis.removeDynamicWall()
                }
            }
        }
        layer.when(() => {
            if (zoomToLayer) this.mapview.goTo(layer.fullExtent)
            // 点击事件
            const pointEventId = ArcGisUtils.mapClickEventHandle.add(layer.id, (point, graphic) => {
                const pointStr = `${point.x},${point.y}`
                if (mapUtil._clickEvtPoint == pointStr) return
                mapUtil._clickEvtPoint = pointStr
                if (graphic) {
                    graphic.attributes.position = point
                    graphic.attributes.data = _attr_lower[graphic.attributes.guid]
                    graphic.attributes.data_origin = _attr_origin[graphic.attributes.guid]
                    if (onclick) onclick(graphic.attributes)
                }
            })
            this._sortClickEvts()
        })
    },
    _createWall(params) {
        const { points, height } = params
        const layer = gis.addDynamicWall({
            view: this.mapview,
            points,
            height: height || 1000,
        });
        params.remove = () => {
            gis.removeDynamicWall()
        }
        return params
    },
    /**
     * @description: 只传layerid时加载三维热力图，传layerid和data时加载指定二维热力图
     * @param {*} params 至少传一个layerid
     * @return {*}
     */
    loadHeatmapLayer(params) {
        let { layerid, type = "2d", hour, onload } = params
        if (this.layers[layerid]) this.removeLayer(layerid)
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        if (params.data && type == "2d") {
            let { data, distance, alpha, threshold } = params
            let heatdata = []
            data.forEach((item, idx) => {
                heatdata.push({
                    lng: item[0],
                    lat: item[1],
                    count: item[2],
                    geohash: idx,
                })
            })
            // let layer = gis.loadHeatmapLayer({
            //     data: heatdata,
            //     colorStops: [
            //         { ratio: 0, color: "rgba(0, 0, 255, 0)" },
            //         { ratio: 0.2, color: "rgba(0, 0, 255, .3)" },
            //         { ratio: 0.4, color: "rgba(0, 0, 255, .6)" },
            //         { ratio: 0.6, color: "rgba(0, 0, 255, 1)" },
            //         { ratio: 0.8, color: "rgba(255, 0, 0, .6)" },
            //         { ratio: 1, color: "rgba(255, 0, 0, 1)" },
            //     ],
            //     maxDensity: 4.8,
            //     radius: 50,
            // })
            let layer = gis.loadHeatmapLayer({
                data: heatdata,
                colorStops: [
                    { color: "rgba(63, 40, 102, 0)", ratio: 0 },
                    { color: "rgba(0, 175, 255, 0.6)", ratio: 0.1 },
                    { color: "rgba(20, 180, 65, 0.6)", ratio: 0.3 },
                    { color: "rgba(255, 250, 0, 0.6)", ratio: 0.7 },
                    { color: "rgba(255, 70, 0, 0.6)", ratio: 1 },
                ],
                maxDensity: 100,
                radius: 10, // 查询半径单位pt
            });
            this.layers[layerid] = layer
        } else if (type == "3d") {
            gis.addHeatMap()
            this.layers[layerid] = {
                remove: () => {
                    gis.removeHeatmap()
                }
            }
        } else if (type == "dynamic") {
            // gis.addMixHeatMap()//混合热力图
            gis.addMix3dHeatmap({ view: window.view, zoom: 13.5 })//混合3d热力图
            this.layers[layerid] = {
                remove: () => {
                    gis.removeMix3dHeatmap()
                }
            }
        }
    },
    loadHeatmapLayer1(params) {
        let { layerid, type = "2d", hour, onload } = params
        if (this.layers[layerid]) this.removeLayer(layerid)
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        if (params.data && type == "2d") {
            let { data, distance, alpha, threshold } = params
            let heatdata = []
            data.forEach((item, idx) => {
                heatdata.push({
                    lng: item.lng,
                    lat: item.lat,
                    count: item.count,
                    geohash: idx,
                })
            })
            // let layer = gis.loadHeatmapLayer({
            //     data: heatdata,
            //     colorStops: [
            //         { ratio: 0, color: "rgba(0, 0, 255, 0)" },
            //         { ratio: 0.2, color: "rgba(0, 0, 255, .3)" },
            //         { ratio: 0.4, color: "rgba(0, 0, 255, .6)" },
            //         { ratio: 0.6, color: "rgba(0, 0, 255, 1)" },
            //         { ratio: 0.8, color: "rgba(255, 0, 0, .6)" },
            //         { ratio: 1, color: "rgba(255, 0, 0, 1)" },
            //     ],
            //     maxDensity: 4.8,
            //     radius: 50,
            // })
            let layer = gis.loadHeatmapLayer({
                data: heatdata,
                colorStops: [
                    { color: "rgba(63, 40, 102, 0)", ratio: 0 },
                    { color: "rgba(0, 175, 255, 0.6)", ratio: 0.1 },
                    { color: "rgba(20, 180, 65, 0.6)", ratio: 0.3 },
                    { color: "rgba(255, 250, 0, 0.6)", ratio: 0.7 },
                    { color: "rgba(255, 70, 0, 0.6)", ratio: 1 },
                ],
                maxDensity: 100,
                radius: 10, // 查询半径单位pt
            });
            this.layers[layerid] = layer
        } else if (type == "3d") {
            gis.addHeatMap()
            this.layers[layerid] = {
                remove: () => {
                    gis.removeHeatmap()
                }
            }
        } else if (type == "dynamic") {
            // gis.addMixHeatMap()//混合热力图
            gis.addMix3dHeatmap({ view: window.view, zoom: 13.5 })//混合3d热力图
            this.layers[layerid] = {
                remove: () => {
                    gis.removeMix3dHeatmap()
                }
            }
        }
    },
    async loadTileLayer(params) {
        const { layerid, url, id, type, renderer, onclick, sublayers } = params
        if (!layerid || !url) {
            console.warn('layerid 或 服务地址 不可为空')
            return
        }
        if (!this._checkBeforeLoad(layerid)) return
        if (['map降雨1', 'map相对湿度1', 'map云量1', 'map气温1', 'map风速1', 'map降雨-面1', 'map相对湿度-面1', 'map云量-面1', 'map气温-面1', 'map风速-面1'].includes(layerid))
            return
        const dzm = {
            "map降雨": "",
            "map相对湿度": "humidity",
            "map云量": "clound",
            "map气温": "temperature",
            "map风速": "wind",
            "map降雨-面": "",
            "map相对湿度-面": "humidity",
            "map云量-面": "clound",
            "map气温-面": "temperature",
            "map风速-面": "wind",
            "降雨": "",
            "相对湿度": "humidity",
            "云量": "clound",
            "气温": "temperature",
            "风速": "wind",
            "降雨-面": "",
            "相对湿度-面": "humidity",
            "云量-面": "clound",
            "气温-面": "temperature",
            "风速-面": "wind",
        }
        let _type = "tile"
        let _url = url

        if (dzm[layerid]) {
            if (this.layers[dzm[layerid]]) {
                this.layers[dzm[layerid]]?.remove()
                delete this.layers[dzm[layerid]]
            }
            this.layers[dzm[layerid]] = await gis.loadWeatherLayer(dzm[layerid], 1)
            this.layers[dzm[layerid]].remove = () => {
                gis.removeWeatherLayer(dzm[layerid], 1)
            }
            this.layers[dzm[layerid]].setOpacity = (opacity) => {
                if (opacity > 0 && opacity <= 1)
                    this.layers[dzm[layerid]].opacity = opacity
            }
            return
        }
        let layerTypeEnum = {
            "Map": "map-image",
            "wms": "wms",
            "wmts": "wmts",
            "IntegratedMesh": "integrated-mesh",
            "Building": "building-scene",
            "Scene": "scene",
            "Tile": "tile"
        }

        if (mapUtil._tcglConfig && mapUtil._tcglConfig[id]) {
            const { name, layertype, layerurl, sublayers } = mapUtil._tcglConfig[id]
            let layerProps = {}
            if (layertype == "wms") {
                layerProps = {
                    type: "wms",
                    title: name,
                    url: layerurl,
                    sublayers: [{
                        name: sublayers
                    }
                    ]
                }
            }
            else if (layertype == "wmts") {
                layerProps = {
                    type: "web-tile",
                    title: name,
                    urlTemplate: layerurl,
                    subDomain: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7",],
                    tileInfo: view.map.basemap.baseLayers._items[0].tileInfo,
                    spatialReference: { wkid: 4490 },
                    fullExtent: {
                        xmin: -180,
                        ymin: -90,
                        xmax: 180,
                        ymax: 90,
                        spatialReference: { wkid: 4490 },
                    }
                }
            }
            else if (layertype == "web-tile" && layerurl.indexOf('gfmap') >= 0) {
                layerProps = {
                    title: "金华影像",
                    urlTemplate: layerurl,
                    subDomains: ["a", "b", "c", "d"],
                    copyright: '',
                    type: "web-tile",
                    spatialReference: { wkid: 4490 },
                }
            }
            else {
                layerProps = {
                    type: layerTypeEnum[layertype] || layertype,
                    url: layerurl,
                    title: layerid,
                }
                // featurelayer图层图标
                if (this.mapcfg?.layers[id]?.renderer) layerProps.renderer = this.mapcfg?.layers[id]?.renderer
                if (this.mapcfg?.layers[id]?.popupTemplate) layerProps.popupTemplate = this.mapcfg?.layers[id]?.popupTemplate
                if ("feature" == layertype) layerProps.outFields = "*"

                if (layertype.indexOf("Scene") >= 0 || layertype.indexOf("Building") >= 0)
                    if ("1558" == id) {
                        layerProps.elevationInfo = {
                            height: 38.2
                        }
                    } else {
                        layerProps.elevationInfo = {
                            mode: "on-the-ground"
                        }
                    }
                if (sublayers) {
                    layerProps.sublayers = [{ id: sublayers, visible: true }]
                }
                if (renderer) layerProps.renderer = renderer
            }
            gis.loadArcgisLayer(this.mapview, layerProps).then(layer => {
                this.layers[layerid] = layer
                if (this.mapcfg?.layers[id]?.camera) {
                    this.mapview.goTo(this.mapcfg?.layers[id]?.camera)
                }
                if (onclick) this._clickEvts[layerid] = onclick
            })
        } else {
            let layerProps = {
                type,
                url,
                title: layerid,
            }
            // featurelayer图层图标
            if (renderer) layerProps.renderer = renderer
            if (sublayers) layerProps.sublayers = sublayers
            gis.loadArcgisLayer(this.mapview, layerProps).then(layer => {
                this.layers[layerid] = layer
            })
            if (onclick) this._clickEvts[layerid] = onclick
        }
    },
    loadWmsLayer(params) {
        this.logInfo("loadWmsLayer", params)
    },
    loadLegend(params) {

    },
    //加载轨迹线
    loadTraceLayer(params) {
        let layerid = params.layerid
        let iconcfg = params.iconcfg || {}
        let linecfg = params.linecfg || {}
        if (!this._checkBeforeLoad(layerid)) return
        let coordinates = params.coordinates
        if (!coordinates || !Array.isArray(coordinates)) {
            console.warn('坐标数据不合法！')
            return
        }
        let data = {
            view: window.view,
            data: coordinates,
            effect: {
                symbolSize: iconcfg.iconSize || [50, 50],
                symbol: iconcfg.image ? `image://${iconcfg.image}` : "image://https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/img/billboard.svg",
                color: "green",
                constantSpeed: 80,
            },
        }
        let trace = ArcGisUtils.TrackLayer.create(data)
        this.layers[layerid] = {
            trace,
            remove: () => {
                trace.destroy()
            }
        }
        /* let data = {
            "funcName": "createTrajectory", // 方法名
            "data": {
                id: params.layerid || "traceLayer",//图层id
                type: "locus",//轨迹线类型 （locus、dynamic）（默认：dynamic 实时轨迹）
                icon: iconcfg.image || 'https://webapi.amap.com/theme/v1.3/markers/b/start.png',//动态轨迹移动标志
                coordinates: coordinates,
                iconStyle: {//动态轨迹移动标志样式
                    "icon-size": iconcfg.iconSize || 2,
                    "icon-rotate": 360,
                },
                style: {//轨迹线样式
                    "line-width": linecfg.width || 10,
                },
                isGlowLine: false,  //是否绘制发光线轨迹
                isBezierCurve: false,//是否以贝塞尔曲线绘制轨迹线
                color: ["red"],//轨迹线颜色
                loop: true,//是否循环播放
                steps: 10000//播放时间（毫秒级）
            },
            "remove": () => {
                if (mapUtil.layers[layerid]) {
                    this.map[layerid].stop()
                    this.map[layerid].remove()
                    delete mapUtil.layers[layerid]
                }
            }
        } */
    },
    loadTrafficLayer(params) {
        this.logInfo("loadTrafficLayer", params)
        const { layerid } = params
        if (!this._checkBeforeLoad(layerid))
            return
        const data = {
            layerid,
            remove: () => {
                gis.removeRoadLayer()
            }
        }
        gis.addRoadLayer()
        this.layers[layerid] = data
    },
    loadFlylieLayer(params) {
        this.logInfo("loadFlylieLayer", params)
    },
    loadRegionLayer_bak(params) {
        const layerid = params.layerid
        if (!this._checkBeforeLoad(layerid)) return
        let colors = []
        params.data.forEach(item => {
            colors.push({
                value: item.name,
                color: item.color
            })
        })
        if (!this.quhuajson) {
            $.ajax({
                type: "get",
                data: {},
                url: "https://csdn.dsjj.jinhua.gov.cn:8101/static/data/bounds.json",
                async: false,
                dataType: "json",
                success: function (res) {
                    mapUtil.quhuajson = res
                }
            });
        }
        if (!this.quhuajsonSubs) {
            this.quhuajsonSubs = []
            this.quhuajson.features.forEach((item, idx) => {
                this.quhuajsonSubs.push({
                    "type": "FeatureCollection",
                    "features": [item]
                })
            })
        }
        let layer = {
            subs: [],
        }
        this.quhuajsonSubs.forEach((item, idx) => {
            let sublayer = gis.addExtrudeLayer({
                view,
                geojson: item,
                height: 2000 * (idx + 1),
                colorMap: {
                    field: "name",
                    colors
                },
                onClick: (data) => {
                    if (params.onclick) params.onclick(data)
                }
            });
            layer.subs.push(sublayer)
        })

        layer.remove = () => {
            gis.removeExtrudeLayer(this.mapview)
            delete this.layers[layerid]
        }
        this.layers[layerid] = layer
        let opts_rk = {
            x: 120.19374002025009,
            y: 27.431223219780183,
            z: 166524.7051093839,
            heading: 354.2661149152383,
            tilt: 47.90202085800513,
            basemap: "vector"
        }
        this.flyTo(opts_rk)
    },
    loadRegionLayer(params) {
      console.log(params)
        const layerid = params.layerid
        if (!this._checkBeforeLoad(layerid)) return
        let colors = []
        params.data.forEach(item => {
            colors.push({
                value: item.name,
                color: item.color
            })
        })
        if (!this.quhuajson) {
            $.ajax({
                type: "get",
                data: {},
                url: "https://csdn.dsjj.jinhua.gov.cn:8101/static/data/bounds.json",
                async: false,
                dataType: "json",
                success: function (res) {
                    mapUtil.quhuajson = res
                }
            });
        }
        this.quhuajson.features.forEach((item, idx) => {
            params.data.forEach(obj => {
                if (obj.name == item.properties.name) {
                    item.properties.height = obj.height
                    item.properties.color = obj.color
                }
            })
        })
        let layer = gis.addExtrudeLayer({
            view,
            geojson: this.quhuajson,
            // height: 1000,
            // colorMap: {
            //     field: "name",
            //     colors
            // },
            colorField: 'color',
            sizeField: 'height',
            onClick: (data) => {
                if (params.onclick) params.onclick(data)
            }
        });
        layer.remove = () => {
            gis.removeExtrudeLayer(this.mapview)
            delete this.layers[layerid]
            // gis.changePoiLayerVisible(this.mapview, true)
            // 设置地图背景不透明
            if (params.showbg) {
                view.map.ground.opacity = 1
            }
        }
        this.layers[layerid] = layer
        // gis.changePoiLayerVisible(this.mapview, false)
        // 设置地图背景透明
        if (params.showbg) {
            view.map.ground.opacity = 0
        }
    },
    loadPopupLayer(params) {
        let { layerid, data = [], style = {}, onclick } = params
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        let list = []
        data.forEach((item, idx) => {
            item.layerid = layerid
            list.push({
                id: `${layerid}_${idx}`,
                position: item.position,
                properties: item,
                content: item.content,
                offset: style.offset,
            })
        })
        const _onclick = function (e) {
            if (onclick) onclick(e.data)
        }
        let layer = gisHelper.createCustomLayer({
            view: this.mapview,
            layerid,
            data: list,
            style: { size: [200, 200], offset: style.offset || [0, 0] },
            onclick: _onclick
        })
        this.layers[layerid] = layer
    },
    /**
     * @description: 加载聚合图层
     * @param {*}
     * params = {
            layerid: 'testCluster',
            data: window.__data,
            lyrCfg: {
              field: "id", // 接口返回值：唯一的字段
              clusterImg: "", // 聚合图标地址
              iconImg: "", // 默认图标地址
              criticalZoom: 1000,
            },
            popCfg: {
              title: 'esType1',//标题
              dict:{
                id:'ID',
                esY:'纬度',
                esX:'经度',
              }
            }
        }
     * @return {*}
     */
    loadClusterLayer(params) {

        let { layerid, data = [], lyrCfg = {}, uniqueCfg = {} } = params
        if (!data || data.length == 0) {
            console.error('上图数据不可为空！', layerid)
            return
        };
        if (!this._checkBeforeLoad(layerid)) {
            return
        }
        const _attr = {}
        const clock = setInterval(() => {
            if (window.view) {
                clearInterval(clock);
                const layerConfig = {
                    code: 1, // code的类型
                    objectIdField: lyrCfg.field || "id", // 接口返回值：唯一的字段
                    clusterImgSrc: lyrCfg.clusterImg || "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/img/circle-cluster.png", // 聚合图标地址
                    defaultImgSrc: lyrCfg.iconImg || "https://csdn.dsjj.jinhua.gov.cn:8101/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-%E5%81%9C%E8%BD%A6%E5%9C%BA.png", // 默认图标地址
                    defaultImgSize: 64,
                    sizeMin:70,//聚合图标最小尺寸
                    sizeMax:150,//聚合图标最大尺寸
                    clusterLabelSym: {
                        type: "text",
                        color: "#fff",
                        haloColor: "black",
                        haloSize: "2px",
                        font: {
                            weight: "normal",
                            size: "50px",
                        },
                    },
                    data, // 接口请求返回的数据列表
                    criticalZoom: params.lyrCfg.criticalZoom || 17,
                    uniqueRenderValueArr: uniqueCfg.valueArr || [],
                    uniqueRenderImgSrcArr: uniqueCfg.imgArr || [],
                    uniqueRenderSizeArr: uniqueCfg.sizeArr || [],
                    defaultField: uniqueCfg.field
                };
                const layer = ArcGisUtils.ClusterLayer.createClusterLayer(
                    view,
                    layerConfig
                )
                const pointEventId = ArcGisUtils.mapClickEventHandle.add(layer.id, (point, graphic, graphics) => {
                    const pointStr = `${point.x},${point.y}`
                    if (mapUtil._clickEvtPoint == pointStr) return
                    mapUtil._clickEvtPoint = pointStr
                    if (graphic && graphics.length == 1) {
                        graphic.attributes.position = point
                        graphic.attributes.data = params.popCfg.attr[graphic.attributes.guid]

                        if (params.popCfg.dict) {
                            this._createPopup({
                                layerid,
                                position: graphic.geometry,
                                dict: params.popCfg.dict,
                                attr: graphic.attributes,
                                title: params.popCfg.title || "详情",
                                offset: params.popCfg.offset,
                            })
                        }
                        setTimeout(() => {
                            $('.mapPopup').css({
                                'background': 'none', 'box-shadow': 'none'
                            })
                            $('.mapPopup .header').css({ display: 'none' })
                            $('.mapPopup div.body::before').css({ display: 'none' })
                        }, 400);
                    }
                    let graArray = []
                    if (graphics) {
                        graphics.forEach(item => {
                            item.attributes.data = params.popCfg.attr[item.attributes.guid]
                            graArray.push(item.attributes)
                        })
                    }
                    if (params.popCfg.onclick) params.popCfg.onclick(graphic.attributes, graArray)
                    // else if (graphics.length > 1) {
                    //     graphic.attributes.data = params.popCfg.attr[graphic.attributes.guid]
                    //     let graArray = []
                    //     if (graphics) {
                    //         graphics.forEach(item => {
                    //             item.attributes.data = params.popCfg.attr[item.attributes.guid]
                    //             graArray.push(item.attributes)
                    //         })
                    //     }
                    //     if (params.popCfg.onclick) {
                    //         params.popCfg.onclick(graphic.attributes,graArray)
                    //     }
                    // else{
                    //         this._createPopup({
                    //             layerid,
                    //             position: point,
                    //             dict: params.popCfg.dict,
                    //             attr: datas,
                    //             title: params.popCfg.title || "详情",
                    //         })
                    //     }
                    // }
                })
                this._sortClickEvts()
                this.layers[layerid] = layer;
            }
        }, 1000);
    },
    removeWmsLayer(layerid) {
        this.logInfo("removeWmsLayer", layerid)
    },
    removeLayer(layerid) {
        // this.logInfo("removeLayer", layerid)
        if (!layerid) return
        if (layerid.indexOf('basemap') >= 0) return
        const dzm = {
            "map降雨": "",
            "map相对湿度": "humidity",
            "map云量": "clound",
            "map气温": "temperature",
            "map风速": "wind",
            "map降雨-面": "",
            "map相对湿度-面": "humidity",
            "map云量-面": "clound",
            "map气温-面": "temperature",
            "map风速-面": "wind",
            "降雨-面": "",
            "相对湿度-面": "humidity",
            "云量-面": "clound",
            "气温-面": "temperature",
            "风速-面": "wind",
        }
        // "降雨": "",
        // "相对湿度": "humidity",
        // "云量": "clound",
        // "气温": "temperature",
        // "风速": "wind",
        if (dzm[layerid])
            layerid = dzm[layerid]
        if (this.layers[layerid]) {
            if (this.mapview) {
                if (this.layers[layerid].remove) {
                    this.layers[layerid].remove()
                    delete this.layers[layerid]
                } else {
                    this.mapview.map.remove(this.layers[layerid])
                    delete this.layers[layerid]
                }
            }
        }
        // if (gis.mapPopupWidget._popupRef) gis.mapPopupWidget.close()
        if (this.blurevts[layerid]) {
            delete this.blurevts[layerid]
        }
        if ("histogram" == layerid) {
            if (gis) gis.removeBarLayer(this.mapview)
        }
        if (layerid == this.popups.layerid) {
            if (gis.mapPopupWidget._popupRef) gis.mapPopupWidget.close()
            this.popups.layerid = null
        }
    },
    removeAllLayers(layerlist = []) {
        Object.keys(this.layers).forEach(layerid => {
            if (this.layers[layerid]) {
                if (layerlist && layerlist.length > 0) {
                    if (layerlist.includes(layerid)) this.removeLayer(layerid)
                } else {
                    this.removeLayer(layerid)
                }
            }
        })
        this.removeLayer("histogram")
    },
    removeLegend(legendId = "legend") {

    },
    removeAllLegends() {
        //TODO:
    },
    removeTrace(id) {

    },
    loadPolylineData(params) {
        let indexid = params.indexid
        params.data = []
        params.lines = []
        this._ajaxQuery(indexid, {}, res => {
            res.data.forEach((line) => {
                let linedata = []
                line.forEach((point) => {
                    linedata.push(point[0])
                    linedata.push(point[1])
                    linedata.push(0)
                })
                params.data.push(linedata)
                params.lines.push(line)
            })
            this.loadPolylineLayer(params)
        })
    },
    loadPointData(params, extData = null) {
        const { indexid, layerid, layercfg, iconcfg, datacfg, popcfg, onclick, args, filter, onblur, cluster } = params
        this._ajaxQuery(indexid, {}, function (res) {
            res.data.forEach(item => {
                if (extData) {
                    Object.assign(item, extData)
                }
            })
            const _onclick = (e) => { }
            mapUtil.loadPointLayer({
                data: res.data,
                layerid: layerid || layercfg.layerid, //图层id
                iconcfg: iconcfg,   //图标
                datacfg: datacfg,   //数据返回的经纬度反了！！！
                onclick: onclick || _onclick,
                popcfg: popcfg,
                onblur,
                cluster,
                args
            })
        })
    },
    loadPolygonData(params, extData = null) {
        const { layerid, indexid, onclick } = params
        defaultKeys = Object.keys(params)
        if (!layerid || !indexid) {
            console.error('layerid 和 url 不可为空')
            return
        }
        let jq = this._ajaxQuery(indexid, {}, (res) => {
            if ('removed' == this.layerstate[layerid]) {
                return
            }
            let list = []
            res.features.forEach((item, idx) => {
                Object.assign(item.properties, extData)
                item.properties.guid = idx
                list.push(item)
            })
            res.features = list
            this.loadPolygonLayer({
                layerid: layerid,
                data: res,
                style: params.style || {},
                onclick: function (e) {
                    // let data = e.data
                    let data = e //在arcgis里自己封装的接口返回的就是属性
                    if (onclick) onclick(data)
                }
            })
        })
    },
    loadRoadVideo(params) {
        const { layerid, videoType, distance, lineStr, onclick, callback } = params
        if (!this._checkBeforeLoad(layerid)) return
        if (!videoType) {
            console.warn("请输入视频类型 videoType")
            return
        }
        if (!lineStr) {
            console.warn("请输入线数据")
            return
        }
        const data = {
            "funcName": 'RoadVideo',
            "videoType": videoType,
            "distance": 50 || distance,
            "lngLat": lineStr || '119.71347391605377,29.1484397649765;119.71315205097198,29.147887229919434',
            remove: () => {
                this.removeAllLayers([layerid + "_line", layerid + "_camera"])
            }
        }
        let paths = []
        if (lineStr.indexOf(';') >= 0) {
            lineStr.split(';').forEach(item => {
                let path = []
                let xy = item.split(',')
                for (var i = 0; i < xy.length; i++) {
                    path.push([xy[i + 0] * 1, xy[i + 1] * 1])
                    i = i + 2
                }
                paths.push(path)
            })
        } else {
            let path = []
            let xy = lineStr.split(',')
            for (var i = 0; i < xy.length; i++) {
                path.push([xy[i + 0] * 1, xy[i + 1] * 1])
                i = i + 2
            }
            paths.push(path)
        }
        /*let layer = gis.addRoadVideoLayer({
            view: this.mapview,
            line: {
                paths: [path]
            },
            bufferDistance: distance || 1000,
            showBuffer: true
        }) || {}
        layer.remove = () => {
            gis.removeRoadVideoLayer(this.mapview)
        }
        this.layers[layerid] = layer*/
        let geojson = {
            "type": "FeatureCollection",
            "features": []
        }
        paths.forEach(item => {
            geojson.features.push({
                "type": "Feature",
                "properties": {},
                "geometry": {
                    "type": "LineString",
                    "coordinates": item
                }
            })
        })
        let url = "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/video/FeatureServer/0/query?f=json&spatialRel=esriSpatialRelIntersects&where=1=1&geometryType=esriGeometryPolyline&inSR=4490&outSR=4490&outFields=*";
        $.ajax({
            type: "get",
            data: {
                geometry: JSON.stringify({ "paths": paths }),
                distance: 50 || distance,
                // geojson: JSON.stringify(geojson)
            },
            url,
            async: false,
            dataType: "json",
            success: (res) => {
                this.removeAllLayers([layerid + "_line", layerid + "_camera"])
                this.loadPolylineLayer({
                    layerid: layerid + "_line",
                    geojson,
                    style: {
                        width: 5,
                        color: "red"
                    }
                })
                let data = []
                res.features.forEach(item => {
                    item.attributes.lng = item.geometry.x
                    item.attributes.lat = item.geometry.y
                    item.attributes.sn = item.attributes.sn + ""
                    if (item.attributes.dev_code) {
                        item.attributes.chn_code = item.attributes.dev_code.substr(0, 20)
                        // if (item.attributes.lablenames.indexOf(videoType) >= 0)
                        data.push(item.attributes)
                    }
                })
                this.layers[layerid + "_line"].when(() => {
                    this.mapview.goTo(this.layers[layerid + "_line"]?.fullExtent?.expand(10));
                })
                this.layers[layerid] = {
                    layerid,
                    lineLayer: this.layers[layerid + "_line"],
                    cameraLayer: this.layers[layerid + "_camera"],
                    remove: () => {
                        this.removeAllLayers([layerid + "_line", layerid + "_camera"])
                    },
                }
                if (callback) callback(data)
            }
        });
        /* this._ajaxQuery(url, {
            geometry: JSON.stringify({ "paths": paths }),
            distance
        }, (res) => {
            this.removeAllLayers([layerid + "_line", layerid + "_camera"])
            this.loadPolylineLayer({
                layerid: layerid + "_line",
                geojson: {
                    "type": "FeatureCollection",
                    "features": [
                        {
                            "type": "Feature",
                            "geometry": {
                                "type": "LineString",
                                "coordinates": paths
                            }
                        }
                    ]
                },
                style: {
                    width: 5,
                    color: "red"
                }
            })
            let data = []
            res.features.forEach(item => {
                item.attributes.lng = item.geometry.x
                item.attributes.lat = item.geometry.y
                item.attributes.sn = item.attributes.sn + ""
                // if (item.attributes.lablenames.indexOf(videoType) >= 0)
                data.push(item.attributes)
            })
            // this.loadPointLayer({
            //     layerid: layerid + "_camera",
            //     data,
            //     iconcfg: {
            //         image: 'camera-zx-qiangji'
            //     },
            //     onclick,
            //     onload: (layer) => {
            //         // layer.when(()=>{
            //         //     this.mapview.goTo(layer?.fullExtent?.expand(0.6));
            //         // })
            //     }
            // })
            this.layers[layerid + "_line"].when(() => {
                this.mapview.goTo(this.layers[layerid + "_line"]?.fullExtent?.expand(10));
            })
            this.layers[layerid] = {
                layerid,
                lineLayer: this.layers[layerid + "_line"],
                cameraLayer: this.layers[layerid + "_camera"],
                remove: () => {
                    this.removeAllLayers([layerid + "_line", layerid + "_camera"])
                },
            }
            if (callback) callback(data)
        }) */
    },
    identify(params) {
        let { point, url, layers = "" } = params
        if (point) {
            url = url + `/identify?geometryType=esriGeometryPoint&tolerance=0.1&mapExtent=180,-90,180,90&imageDisplay=1&returnGeometry=true&returnZ=false&returnM=false&returnUnformattedValues=false&returnFieldName=true&f=json`
            this._ajaxQuery(url, {
                geometry: `{x:${point.x},y:${point.y}}`,
                layers
            }, (res) => {
                if (params.callback) params.callback(res.results)
            })
        }
    },
    findTask(params) {
        const { key = "沙畈乡", type, loadToMap, layerid, callback, onclick } = params
        const cfg = {
            "sqgrid": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZSQ&layers=3&f=json',
            },
            "grid": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZZ&layers=3&f=json',
            },
            "shequ": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZZ&layers=2&f=json',
            },
            "street": {
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/find?searchFields=SZQX&layers=1&f=json',
            },
            "qx": {
                url: ""
            }
        }
        if (cfg[type]) {
            this._ajaxQuery(cfg[type].url, {
                searchText: key,
            }, (res) => {
                let data = {
                    "type": "FeatureCollection",
                    "features": []
                }
                res.results.forEach(item => {
                    data.features.push(ArcgisToGeojsonUtils.arcgisToGeoJSON(item))
                })
                if (callback) callback(data)
            })
        }
    },
    queryByDistance(params) {
        const { geometry, geometryType = "esriGeometryPoint", type = "video", distance, callback } = params
        let urls = {
            "video": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/video/FeatureServer/0/query?f=json&spatialRel=esriSpatialRelIntersects&where=1=1&geometryType=esriGeometryPolyline&inSR=4490&outSR=4490&outFields=*"

        }
        $.ajax({
            type: "get",
            data: {
                geometry,
                distance: 50 || distance,
            },
            url: urls[type],
            async: false,
            dataType: "json",
            success: (res) => {
                if (callback) callback(data)
            }
        });
    },
    //火焰加载
    loadFireEffect(params) {
        ArcGisUtils.addFireEffect(params)
    },
    //火焰清除
    clearFireEffect() {
        ArcGisUtils.removeFireEffect()
    },
    //烟雾加载
    loadFireSmokeEffect(params) {
        ArcGisUtils.addFireSmokeEffect(params)
    },
    //烟雾清除
    clearFireSmokeEffect() {
        ArcGisUtils.removeFireSmokeEffect()
    },
    //雷达扫描
    loadRadarMask(params) {
        const { layerid, radius, center } = params
        if (!this._checkBeforeLoad(layerid)) return;

        let data = [{
            radius: radius * 1000, // 半径km
            position: [center[0], center[1], 0], // 中心点坐标
        }]
        gis.addRadarMask({ view: this.mapview, data })
        params.remove = () => {
            gis.removeRadarMask(this.mapview);
        }
        this.layers[layerid] = params
    },
    //点报警效果
    loadPointAlarm: {
        _scatterObj: null,
        active(params) {
            this._scatterObj = ArcGisUtils.ScatterToMap.create(mapUtil.mapview, params)
        },
        close() {
            this._scatterObj && this._scatterObj.destroy();
        }
    },
    //扩散效果
    loadKS(params) {
        let view = mapUtil.mapview
        let param = { view, 'data': params }
        this.logInfo(param)
        let layer = ArcGisUtils.addCylinderLayer(param)
    },
    //清除扩散效果
    clearKS() {
        ArcGisUtils.removeCylinderLayer(view);
    },
    //动态墙体
    loadWall(params) {
        let view = mapUtil.mapview
        let param = { view, 'points': params.data, 'height': params.height }
        this.logInfo(param)
        let layer = ArcGisUtils.addDynamicWall(param)
    },
    //清除动态墙体
    clearWall() {
        ArcGisUtils.removeDynamicWall();
    },
    loadTextLayer(params) {
        let view = mapUtil.mapview
        let layerid = params.layerid
        if (!this._checkBeforeLoad(layerid))
            return
        let data = params.data
        let fontSize = params.style.size
        let fontColor = params.style.color
        let textIcon = params.style.icon
        let param = { view, data, fontSize, fontColor }
        if (textIcon) param.textIcon = textIcon
        if (params.style.iconSize) param.iconSize = params.style.iconSize
        let layer = ArcGisUtils.load3DTextLayer(param)
        this.layers[layerid] = layer
    },
    loadExtrusionLayer(params) {
        const { layerid, geojson, zoomToLayer = false } = params
        if (!this._checkBeforeLoad(layerid)) return
        if (!params.geojson) {
            console.warn("矢量面数据不可为空！")
            return
        }
        params.geojson.features.forEach(item => {
            item.properties.layerid = layerid
            if (!item.properties.color) item.properties.color = "white"
            if (!item.properties.height) item.properties.height = 1000
        })
        const data = {
            funcName: 'addfillExtrusionLayer',
            layerid,
            data: {
                id: layerid, // 图层id
                height: params.height || 50, //拉升高度
                colorName: params.colorName || 'color', //通过properties中的字段来渲染矢量面颜色，在不传color字段时通过此字段渲染颜色
                clorArray: params.colorArray || [], // 通过mapbox过滤器渲染矢量面颜色，在不传color与colorName时通过此字段渲染颜色，参考地址：https://zhuanlan.zhihu.com/p/479368261
                opacity: params.opacity || 0.99, // 透明度0-1
                geojson: params.geojson, // 面数据geojson
                isMessage: true, // true为点击返回消息
            },
        }
        /* let layer = gis.addExtrudeLayer({
            view,
            geojson: params.geojson,
            height: params.height || 50,
            opacity: params.opacity || 0.99, // 选填，默认为1
            color: "blue", // 单一颜色模式
            onClick: (data) => {
                if (params.onclick) params.onclick(data)
            }
        }); */
        let layer = gis.addExtrudeLayer({
            view,
            geojson,
            colorField: "color",
            sizeField: "height",
            isGoto: zoomToLayer,
            onClick: (data) => {
                if (params.onclick) params.onclick(data)
            }
        })
        layer.remove = () => {
            gis.removeExtrudeLayer(this.mapview)
            delete this.layers[layerid]
            // gis.changePoiLayerVisible(this.mapview, true)
        }
        this.layers[layerid] = layer
        // gis.changePoiLayerVisible(this.mapview, false)

    },
    loadHistogram(params) {
        const { data } = params
        if (!this._checkBeforeLoad("histogram"))
            return
        let newdata = [], maxHeight = 40000
        const center = {
            '浦江县': [119.94315399169922, 29.5630503845215, 500],
            '兰溪市': [119.46214447021484, 29.28517738342285, 500],
            '婺城区': [119.5569204711914, 29.00677101135254, 500],
            '金义新区': [119.8483056640625, 29.188559951782227, 500],
            '义乌市': [120.08206787109375, 29.322123641967773, 500],
            '武义县': [119.7269204711914, 28.79677101135254, 500],
            '永康市': [120.1469204711914, 28.97677101135254, 500],
            '东阳市': [120.4169204711914, 29.24677101135254, 500],
            '磐安县': [120.6299204711914, 29.06677101135254, 500]
        }
        let max = Math.max.apply(Math, data.map(item => { return item.num }))
        data.forEach(item => {
            newdata.push({
                position: center[item.name],
                size: [2000, 2000, maxHeight * item.num / max],
                color: "rgb(64, 158, 255)"
            })
        })
        this.layers["histogram"] = {
            remove: () => {
                gis.removeBarLayer(this.mapview)
                delete this.layers["histogram"]
            }
        }
        gis.addBarLayer({
            view: this.mapview,
            data: newdata
        })
    },
    loadWeatherEffect(params = {}) {
        if (!mapUtil.mapview) return
        let city = params.city || '婺城区';
        // let gdCityCode = {
        //     'city': '330700',//金华城市代码
        // };
        // let gdUrl = 'https://restapi.amap.com/v3/weather/weatherInfo?key=c38c99c7f0a453292560e1a167de06b9';
        let cityCode = {
            '金义新区': '101210901',
            '开发区': '101210901',
            '婺城区': '101210901',
            '兰溪市': '101210903',
            '浦江县': '101210902',
            '义乌市': '101210904',
            '东阳市': '101210905',
            '磐安县': '101210908',
            '永康市': '101210907',
            '武义县': '101210906',
        }
        let url = `https://csdn.dsjj.jinhua.gov.cn:8101/jhyjzh-server/screen_api/weather/city?cityCode=${cityCode[city]}`;
        function getWeather() {
            if (mapUtil._weatherSwitch)
                $.ajax({
                    type: "get",
                    // data: city,
                    url: url,
                    async: true,
                    dataType: "json",
                    success: function (e) {
                        // mapUtil._weatherSwitch && weatherGetSuc(d.lives[0].weather)
                        mapUtil._weatherSwitch && weatherGetSuc(e.data.data.forecast[0].type)
                    },
                    error: function (err) {
                        console.log(err);
                    }
                })
        }
        if (mapUtil._weatherTimer) {
            clearInterval(mapUtil._weatherTimer)
        }
        mapUtil._weatherSwitch && (mapUtil._weatherTimer = setInterval(getWeather, 1000 * 60 * 3))
        let weatherType = {
            "晴": { type: "sunny", cloudCover: 0, },
            "少云": { type: "sunny", cloudCover: 0.3, },
            "晴间多云": { type: "sunny", cloudCover: 0.6, },
            "有风": { type: "sunny", cloudCover: 0, },
            "平静": { type: "sunny", cloudCover: 0, },
            "微风": { type: "sunny", cloudCover: 0, },
            "和风": { type: "sunny", cloudCover: 0, },
            "清风": { type: "sunny", cloudCover: 0, },
            "强风/劲风": { type: "sunny", cloudCover: 0, },
            "疾风": { type: "sunny", cloudCover: 0, },
            "大风": { type: "sunny", cloudCover: 0, },
            "烈风": { type: "sunny", cloudCover: 0, },
            "风暴": { type: "sunny", cloudCover: 0, },
            "狂爆风": { type: "sunny", cloudCover: 0, },
            "飓风": { type: "sunny", cloudCover: 0, },
            "热带风暴": { type: "sunny", cloudCover: 0, },
            "雪": { type: "snowy", precipitation: 0.5, },
            "阵雪": { type: "snowy", precipitation: 0.8, },
            "小雪": { type: "snowy", precipitation: 0.3, },
            "中雪": { type: "snowy", precipitation: 0.6, },
            "大雪": { type: "snowy", precipitation: 0.9, },
            "暴雪": { type: "snowy", precipitation: 1, },
            "小雪-中雪": { type: "snowy", precipitation: 0.5, },
            "中雪-大雪": { type: "snowy", precipitation: 0.8, },
            "大雪-暴雪": { type: "snowy", precipitation: 1, },
            "阵雨": { type: "rainy", precipitation: 0.5, },
            "雷阵雨": { type: "rainy", precipitation: 0.8, },
            "雷阵雨并伴有冰雹": { type: "rainy", precipitation: 0.8, },
            "小雨": { type: "rainy", precipitation: 0.3, },
            "中雨": { type: "rainy", precipitation: 0.5, },
            "大雨": { type: "rainy", precipitation: 0.8, },
            "暴雨": { type: "rainy", precipitation: 0.9, },
            "大暴雨": { type: "rainy", precipitation: 1, },
            "特大暴雨": { type: "rainy", precipitation: 1, },
            "强阵雨": { type: "rainy", precipitation: 1, },
            "强雷阵雨": { type: "rainy", precipitation: 1, },
            "极端降雨": { type: "rainy", precipitation: 1, },
            "毛毛雨/细雨": { type: "rainy", precipitation: 0.1, },
            "雨": { type: "rainy", precipitation: 0.3, },
            "小雨-中雨": { type: "rainy", precipitation: 0.3, },
            "中雨-大雨": { type: "rainy", precipitation: 0.6, },
            "大雨-暴雨": { type: "rainy", precipitation: 0.8, },
            "暴雨-大暴雨": { type: "rainy", precipitation: 1, },
            "大暴雨-特大暴雨": { type: "rainy", precipitation: 1, },
            "雨雪天气": { type: "rainy", precipitation: 0.8, },
            "雨夹雪": { type: "rainy", precipitation: 0.8, },
            "阵雨夹雪": { type: "rainy", precipitation: 0.8, },
            "冻雨": { type: "rainy", precipitation: 0.8, },
            "霾": { type: "foggy", fogStrength: 0.1, },
            "中度霾": { type: "foggy", fogStrength: 0.3, },
            "重度霾": { type: "foggy", fogStrength: 0.5, },
            "严重霾": { type: "foggy", fogStrength: 0.6, },
            "雾": { type: "foggy", fogStrength: 0.1, },
            "浓雾": { type: "foggy", fogStrength: 0.3, },
            "强浓雾": { type: "foggy", fogStrength: 0.5, },
            "轻雾": { type: "foggy", fogStrength: 0.1, },
            "大雾": { type: "foggy", fogStrength: 0.5, },
            "特强浓雾": { type: "foggy", fogStrength: 0.7, },
            "多云": { type: "cloudy", cloudCover: 0.8, },
            "阴": { type: "cloudy", cloudCover: 1, },
        }

        function weatherGetSuc(gdWeather) {
            console.log(city, gdWeather);
            switch (weatherType[gdWeather].type) {//根据高德返回天气单一值判断
                case 'sunny':
                    mapUtil.mapview.environment.weather = {
                        type: params.type || 'sunny',
                        cloudCover: (params.cloudCover ? params.cloudCover : weatherType[gdWeather].cloudCover) || 0.4
                    }
                    break;
                case 'snowy':
                    mapUtil.mapview.environment.weather = {
                        type: params.type || 'snowy',
                        cloudCover: params.cloudCover || 0.4,
                        precipitation: (params.precipitation ? params.precipitation : weatherType[gdWeather].precipitation) || 0.4,
                    }
                    break;
                case 'rainy':
                    mapUtil.mapview.environment.weather = {
                        type: params.type || 'rainy',
                        cloudCover: params.cloudCover || 0.4,
                        precipitation: (params.precipitation ? params.precipitation : weatherType[gdWeather].precipitation) || 0.4,
                    }
                    break;
                case 'foggy':
                    mapUtil.mapview.environment.weather = {
                        type: params.type || 'foggy',
                        fogStrength: (params.fogStrength ? params.fogStrength : weatherType[gdWeather].fogStrength) || 0.4,
                    }
                    break;
                case 'cloudy':
                    mapUtil.mapview.environment.weather = {
                        type: params.type || 'cloudy',
                        cloudCover: (params.cloudCover ? params.cloudCover : weatherType[gdWeather].cloudCover) || 0.4
                    }
                    break;
            }
        }

        mapUtil._weatherSwitch && getWeather()
    },
    fitBounds(geojson) {

    },
    flyTo(params) {
        if (!this.mapview) return
        const { x, y, z, heading, tilt = 0, offset } = params
        if (params.destination) {
            const [lng, lat] = params.destination
            if (offset) {
                params.view = this.mapview
                gisHelper.flyTo(params)
                return
            }
            this.mapview.goTo({
                center: [lng * 1, lat * 1] || [119.6535, 29.0823],
                zoom: params.zoom || 13, tilt
            })
        }
        if (x && y && heading && tilt) {
            this.mapview.goTo({
                position: {
                    spatialReference: {
                        wkid: 4490,
                    },
                    x,
                    y,
                    z: z ?? 0,
                },
                heading,
                tilt,
            });
        }
    },
    loadModelLayer(params) {
        const { layerid, type, color = [255, 255, 255, 0.6], edgesColor = [0, 0, 0, 0.6], edgesSize = 0.7 } = params
        if (!this._checkBeforeLoad(layerid)) return
        switch (type) {
            case "bm":
                // gis.addWhiteModalLayer(this.mapview, color) //添加白膜
                gis.addGRADIENTBAIMOLayer(this.mapview, [7, 43, 115], [150, 183, 242], [150, 183, 242])//渐变白模
                params.remove = () => {
                    // gis.removeWhiteModalLayer(this.mapview)
                    gis.removeGRADIENTBAIMOLayer(this.mapview)
                }
                break;
            case "jm":
                gis.addPureModalLayer(this.mapview)  //添加精模
                params.remove = () => {
                    gis.removePureModalLayer(this.mapview)
                }
                break;
            case "qx":
                gis.addIntegratedMesh(this.mapview)//倾斜摄影
                params.remove = () => {
                    gis.removeIntegratedMesh(this.mapview)
                }
                break;
            case "dx":
                gis.addDEMToMap()//地形
                params.remove = () => {
                    gis.removeDEMFromMap()
                }
                break;
            default:
                break;
        }
        this.layers[layerid] = params
    },
    async loadWeatherLayer(params) {
        if (!params) return
        const { type, hour, layerid } = params

        if (this.layers[type]) {
            this.layers[type].remove()
            delete this.layers[type]
        }
        let layer;
        switch (type) {
            case "humidity":
                layer = await gis.loadWeatherLayer('humidity', hour)
                layer.remove = () => {
                    gis.removeWeatherLayer('humidity', hour)
                }
                break;
            case "temperature":
                layer = await gis.loadWeatherLayer('temperature', hour)
                layer.remove = () => {
                    gis.removeWeatherLayer('temperature', hour)
                }
                break;
            case "clound":
                layer = await gis.loadWeatherLayer('clound', hour)
                layer.remove = () => {
                    gis.removeWeatherLayer('clound', hour)
                }
                break;
            case "wind":
                layer = await gis.loadWeatherLayer('wind', hour)
                layer.remove = () => {
                    gis.removeWeatherLayer('wind', hour)
                }
            default:

                break;
        }
        if (layer) {
            layer.setOpacity = (opacity) => {
                if (opacity > 0 && opacity <= 1)
                    layer.opacity = opacity
            }
        }
        this.layers[type] = layer
    },
    tool: {
        zoomOut() {
            if (mapUtil.mapview) {
                let zm = mapUtil.mapview.zoom - 1
                mapUtil.mapview.goTo({
                    target: mapUtil.mapview.center,
                    zoom: zm
                });
            }
        },
        zoomIn() {
            if (mapUtil.mapview) {
                let zm = mapUtil.mapview.zoom + 1
                mapUtil.mapview.goTo({
                    target: mapUtil.mapview.center,
                    zoom: zm
                });
            }
        },
        changeBaseMap(type = "black") {
            if (!mapUtil.mapview) return
            const typeEnum = {
                "black": "vector",
                "img": "image",
                "gfimg": "newImage"
            }
            if (!mapUtil.mapview)
                return;
            let view = mapUtil.mapview;
            let code = localStorage.getItem("QRCode")
            "gfimg" == type ? gis.exchangeMap(view, typeEnum[type], code) : gis.exchangeMap(view, typeEnum[type])
            mapUtil._baseMapType = type
        },
        pickPoint(callback) {
            if (!this._pickEvent) {
                this._pickEvent = (e) => {
                    if (callback) callback(e)
                }
                ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {
                    const { x, y } = point;
                    if (this._pickEvent) this._pickEvent({ "lng": x, "lat": y })
                });
            }
        },
        _startPick() {

        },
        stopPick() {
            if (this._pickEvent) {
                ArcGisUtils.mapClickEventHandle._coordinateClientList = []
                this._pickEvent = null
            }
        },
        backHome() {
            if (mapUtil.mapview) mapUtil.mapview.goHome()
        },
        changeMode(mode) {
            if (!this.mapview) return
            gis.change2DOr3D(this.mapview, mode == 2 ? '2D' : '3D')
        },
        change3D(type) {
            if (!mapUtil.mapview)
                return
            if (1 == type) { // 切换到二维
                mapUtil.mapview.goTo({
                    target: mapUtil.mapview.center,
                    tilt: 0,
                });
            }
            else if (10 == type) {// 切换到3d
                mapUtil.mapview.goTo({
                    target: mapUtil.mapview.center,
                    tilt: 47.03419627587757,
                });
            }
            //首屏（没有左右侧面板）初始化视角，加精模
            else if (11 == type) {
                mapUtil.mapview.goTo({
                    position: {
                        spatialReference: {
                            wkid: 4490,
                        },
                        x: 119.62357755062095,
                        y: 29.059796274571504,
                        z: 1851.2331084022298,
                    },
                    heading: 0,//350.3785610052847,
                    tilt: 59.95894739193048,
                });
                mapUtil.loadModelLayer({
                    layerid: "精模",
                    type: "jm"
                })
                // mapUtil._weatherSwitch = true
                // mapUtil.loadWeatherEffect()
            }
            //切换回原来大脑首页（有左右侧面板）初始化视角，去精模
            else if (12 == type) {
                mapUtil.mapview.goTo({
                    position: {
                        spatialReference: {
                            wkid: 4490,
                        },
                        x: 119.65842342884746,
                        y: 28.97890877935061,
                        z: 10280.48295974452,
                    },
                    heading: 0,// 354.2661149152386,
                    tilt: 47.902020858006175,
                });
                mapUtil.removeLayer("精模")
                mapUtil._weatherSwitch = false
            }
        },
        changeScene(type = 'day') {

        },
        addEffect(type) {
            switch (type) {
                case "flowline":
                    gis.addOdLineEffect({
                        queryUrl: 'https://geoplat.geoscene.cn/server/rest/services/daluzizao/MapServer/41',
                        color: '#16D3D8',
                        size: 3,
                        length: 0.4,
                        speed: 0.2,
                        isShow: false,
                    })
                    break;

                default:
                    break;
            }
        },
        removeEffect(type) {
            switch (type) {
                case "flowline":
                    gis.removeOdLineEffect()
                    break;

                default:
                    break;
            }
        }
    },
    searchTool: {
        _layerlist: null,
        _style: {},
        results: [],
        promiseObjs: [],
        _init() {
            mapUtil._ajaxQuery('http://localhost:38400/jhgt/jhgt107/32a9c9a2bec34f6f9478288e32de3314/arcgis/rest/services/GCS330700_3004_CLDJ/GCS330700_3004_CLDJ_DRKQSDJ/MapServer/0/query?outfields=*&f=geojson&where=kqmc%20like%20%27%25%E5%AE%89%E5%9C%B0%E6%B0%B4%E5%BA%93%25%27&resultRecordCount=3', {}, res => {
                mapUtil.logInfo("连接jhgt服务")
            })
        },
        _getLayerlist(cb) {
            this._init()
            if (this._layerlist) return this._layerlist
            mapUtil._ajaxQuery('https://www.fastmock.site/mock/f2ecc6c32f4a68dfaedf6c429d8e24be/jhcsdn/map/searchtool/config', {}, res => {
                this._layerlist = res.data.layerlist
                this._style = res.data.searchTool.style
                if (cb) cb(res)
            })
        },
        _getLayercfg(id) {
            if (!this._layerlist) return null
            for (let i = 0; i < this._layerlist.length; i++) {
                if (this._layerlist[i].id == id) {
                    return this._layerlist[i]
                }
            }
            return null
        },
        _getQueryPromise(key, selectedLayer) {
            let layercfg = this._getLayercfg(selectedLayer.id)
            let obj = {}
            if (layercfg) {
                let p1 = new Promise((resolve, reject) => {
                    // mapUtil._ajaxQuery(layercfg.resturl + `/0/query?where=${layercfg.keyfield} like '%${key}%'&outfields=*&resultRecordCount=10&f=json`, {}, res => {
                    mapUtil._ajaxQuery(layercfg.resturl + `/0/query?outfields=*&f=geojson`, {
                        where: `${layercfg.keyfield} like '%${key}%'`,
                        resultRecordCount: 3
                    }, res => {
                        res.layerid = "searchTool_result_" + selectedLayer.id
                        res.id = selectedLayer.id
                        res.keyfield = layercfg.keyfield
                        resolve(res)
                        obj.abort = reject
                    })
                });
                obj.promise = p1;
                return obj;
            }
            return null
        },
        _getPromiseList(key, selectedLayers) {
            let promises = []
            selectedLayers.forEach(layer => {
                let promise = this._getQueryPromise(key, layer)
                if (promise) {
                    promises.push(promise.promise)
                    this.promiseObjs.push(promise)
                }
            })
            return promises
        },
        _setResult(item) {
            if (item.features.length > 0) {
                item.features.forEach((f, idx) => {
                    let geojson = { ...item }
                    geojson.features = [f]
                    geojson.label = f.properties[item.keyfield]
                    geojson.id = item.layerid + "_" + idx
                    this.results.push(geojson)
                })
            }
        },
        searchByKey(key, selectedLayers, callback) {
            this.clear()
            if (this.promiseObjs) {
                this.promiseObjs.forEach(item => {
                    item.abort()
                })
            }
            let promises = this._getPromiseList(key, selectedLayers)
            if (promises.length < 1) {
                console.warn("当前勾选的图层尚未配置搜索，请勾选【2022年生态保护红线（202210版）-面】来测试")
                return
            }
            Promise.all(promises).then(res => {
                mapUtil.logInfo(res)
                res.forEach((item, idx) => {
                    if (item.features.length > 0) {
                        const type = item.features[0].geometry.type
                        if (type == "Polygon") {
                            mapUtil.loadPolygonLayer({
                                layerid: item.layerid,
                                data: item,
                                style: this._style.polygon,
                                onclick: (e) => {
                                    mapUtil.logInfo(e)
                                }
                            })
                        }
                        else if (type == "Point") {
                            //todo:
                        }
                        else if (type == "LineString") {
                            mapUtil.loadPolylineLayer({
                                layerid: item.layerid,
                                data: item,
                                style: this._style.polyline,
                                onclick: (e) => {
                                    mapUtil.logInfo(e)
                                }
                            })
                        }
                        this._setResult(item, key)
                    }
                })
                if (callback) callback(this.results)
            })
        },
        zoomTo(id) {
            this.results.forEach((item) => {
                if (item.id == id) {
                    mapUtil.fitBounds(item)
                }
            })
        },
        clear() {
            Object.keys(mapUtil.layers).forEach(key => {
                if (key.startsWith('searchTool_result_')) {
                    mapUtil.removeLayer(key)
                }
            })
            this.results = []
        }
    },
    plotTool: {
        _tool: null,
        _currType: "",
        _oncomplete: null,
        _typeEnum: {
            "point": "point",
            "polygon": "polygon",
            "line": "polyline",
            "circle": "circle",
            "rect": "rectangle",
        },
        active(type = "polygon", oncomplete, once = true) {
            if (!this._tool) {
                this._tool = new gis.Draw({ view: mapUtil.mapview })
            }
            this._tool.draw(this._typeEnum[type]).then(e => {
                let res = ArcgisToGeojsonUtils.arcgisToGeoJSON(e)
                if (!res.properties) res.properties = {}
                if (this._typeEnum[type] == "circle") {
                    //计算半径
                    let la1 = e.geometry.centroid.latitude
                    let ln1 = e.geometry.centroid.longitude
                    let la2 = e.geometry.rings[0][0][1]
                    let ln2 = e.geometry.rings[0][0][0]

                    function GetDistance(lat1, lng1, lat2, lng2) {
                        let radLat1 = lat1 * Math.PI / 180.0;
                        let radLat2 = lat2 * Math.PI / 180.0;
                        let a = radLat1 - radLat2;
                        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
                        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                            Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
                        s = s * 6378.137;// EARTH_RADIUS;
                        s = Math.round(s * 10000) / 10000;
                        return s;
                    }
                    // 调用 return的距离单位为km

                    let radiu = GetDistance(la1, ln1, la2, ln2)
                    let cent = { lat: la1, lng: ln1 }
                    let are = Math.PI * Math.pow(radiu, 2)
                    let circleresult = { radius: radiu, center: cent, area: are, coordinates: e.geometry.rings }
                    if (oncomplete) {
                        oncomplete(circleresult)
                        if (!once) {
                            mapUtil.plotTool.active(type, oncomplete)
                        } else {
                            this._tool.cancel()
                        }
                    }
                } else {
                    if (this._typeEnum[type] == "polygon") {
                        res.properties.area = turf.area(res)
                    }
                    if (oncomplete) {
                        oncomplete(res)
                        if (!once) {
                            mapUtil.plotTool.active(type, oncomplete)
                        } else {
                            this._tool.cancel()
                        }
                    }
                }

            })

        },
        close() {
            if (this._tool) {
                this._tool.clear()
                this._tool.destroy()
                this._tool = null
            }
        },
        clear() {
            if (this._tool) {
                this._tool.clear()
            }
        }
    },
    drawTool: {
        layers: {},
        draw(type, opts) {
            let params = null
            let layerid = opts.layerid || 'drawtool_circle'
            switch (type) {
                case "circle":
                    this.clear([layerid])
                    let _circlelist = opts.circles
                    params = {
                        "funcName": "drawCircle",
                        "circleData": []
                    }
                    this.layers[layerid] = {
                        funcName: "drawCircle",
                        subs: [],
                        clear: function () {
                            if (this.subs) this.subs.forEach(item => {
                                item.remove()
                            })
                        }
                    }
                    _circlelist.forEach((item, idx) => {
                        item.center.push(1000) //来自易利，不知道第三个参数是干嘛的，可能多了个高度
                        let _strokeColor = item.strokeColor || [0, 255, 255, 1]
                        const _outlineColor = `rgba(${_strokeColor[0]},${_strokeColor[1]},${_strokeColor[2]},${_strokeColor[3]})`
                        let _circledata = {
                            coords: item.center,
                            r: item.radius,
                            color: item.fillColor || [0, 255, 255, 1],
                            colorOutline: _outlineColor,
                            name: `${layerid}_${idx}`
                        }
                        params.circleData.push(_circledata)
                        const _circleGra = gisHelper.createCircle({
                            center: item.center,
                            radius: item.radius ? item.radius / 1000 : 5,
                            strokeColor: _strokeColor,
                            color: item.fillColor || [0, 255, 255, 1],
                            view: mapUtil.mapview,
                            layer: gisHelper.createGraphicsLayer(mapUtil.mapview)
                        })
                        this.layers[layerid].subs.push(_circleGra)
                    })
                    break;
                case "polygon":
                    break;
                default:
                    break;
            }
        },
        clear(ids) {
            if (ids && ids.length) {
                ids.forEach(item => {
                    this.layers[item] && this.layers[item].clear()
                    this.layers[item] = null
                })
            } else {
                Object.keys(this.layers).forEach(layerid => {
                    this.layers[layerid] && this.layers[layerid].clear()
                })
                this.layers = {}
            }
        }
    },
    measureTool: {
        listener: null,
        active(type = "distance", opts = {}) {
            let data = null
            switch (type) {
                case "distance":
                    data = {
                        funcName: 'getDistance',
                        color: opts.color || [255, 0, 0, 1],
                        pointAndTextStatus: false // 是否展示文字和点位
                    }
                    break;
                case "area":
                    data = {
                        funcName: 'startDrawPolygon',
                        color: opts.color || [0, 0, 255, 0.4],//绘制区域颜色
                        pointAndTextStatus: false, // 是否展示文字和点位
                        getSectionDatas: false,//是否查询区域数据，默认false
                    }
                    break;
                default:
                    break;
            }
            if (data) {
                mapUtil._postData(data)
                if (!this.listener) {
                    this.listener = function (e) {
                        let result = { action: "", distance: 0, area: 0 }
                        let data = e.data
                        switch (data.type) {
                            case "line":
                                result.action = "测距"
                                result.distance = data.getDistance
                                break;
                            case "Polygon":
                                result.action = "测面"
                                result.area = data.getDistance
                                break;
                            default:
                                break;
                        }
                        mapUtil.logInfo(result)
                    }
                    window.addEventListener('message', this.listener)
                }
            }
        },
        close() {
            if (this.listener) window.removeEventListener('message', this.listener)
            mapUtil._postData({
                "funcName": "rmLine", //结束绘制多边形
            })
            mapUtil._postData({
                "funcName": "rmDrawPolygon", //结束绘制多边形
            })
        }
    },
    _onload() {
        const _0x3587ba = _0x43d4; (function (_0x3de0bd, _0x201ab9) { const _0x3b5e0b = _0x43d4, _0x4edd88 = _0x3de0bd(); while (!![]) { try { const _0x89419b = -parseInt(_0x3b5e0b(0x9b)) / 0x1 + parseInt(_0x3b5e0b(0x95)) / 0x2 * (parseInt(_0x3b5e0b(0xbf)) / 0x3) + parseInt(_0x3b5e0b(0xd2)) / 0x4 * (parseInt(_0x3b5e0b(0xc8)) / 0x5) + -parseInt(_0x3b5e0b(0xd4)) / 0x6 * (parseInt(_0x3b5e0b(0xab)) / 0x7) + parseInt(_0x3b5e0b(0xb4)) / 0x8 + parseInt(_0x3b5e0b(0x9e)) / 0x9 * (parseInt(_0x3b5e0b(0x8a)) / 0xa) + -parseInt(_0x3b5e0b(0x8b)) / 0xb * (parseInt(_0x3b5e0b(0xa0)) / 0xc); if (_0x89419b === _0x201ab9) break; else _0x4edd88['push'](_0x4edd88['shift']()); } catch (_0x1371ce) { _0x4edd88['push'](_0x4edd88['shift']()); } } }(_0x3156, 0xa744c), this[_0x3587ba(0xd1)] = null, this['\x6d\x61\x70\x76\x69\x65\x77'] = null, this[_0x3587ba(0x94)] = {}, this[_0x3587ba(0xa9)] = {}, this[_0x3587ba(0xd3)] = {}, this[_0x3587ba(0xa7)] = {}, this[_0x3587ba(0xc3)] = '', this[_0x3587ba(0x9c)] = {}, this[_0x3587ba(0xcd)] = () => { const _0x576c05 = _0x3587ba; window[_0x576c05(0x9d)](_0x576c05(0x8e), function (_0x5a7bbf) { const _0x53fbe8 = _0x576c05; let { action: _0x1499f4, params: params = JSON['\x73\x74\x72\x69\x6e\x67\x69\x66\x79']({ '\x6f\x6e\x63\x6c\x69\x63\x6b': ![] }) } = _0x5a7bbf?.[_0x53fbe8(0x99)] || {}; if (_0x1499f4 && _0x1499f4['\x69\x6e\x64\x65\x78\x4f\x66'](_0x53fbe8(0xbc)) == 0x0) { const _0x4d2964 = JSON[_0x53fbe8(0xa8)](params); _0x4d2964[_0x53fbe8(0xa6)] && (_0x4d2964[_0x53fbe8(0xa6)] = function (_0x197147) { const _0x30b8e0 = _0x53fbe8; mapUtil[_0x30b8e0(0xc9)]({ '\x61\x63\x74\x69\x6f\x6e': _0x1499f4, '\x64\x61\x74\x61': _0x197147 }); }), eval(_0x1499f4)[_0x53fbe8(0x9a)](mapUtil, _0x4d2964); } }); }, this['\x5f\x70\x6f\x73\x74\x4d\x73\x67\x54\x6f\x53\x75\x62\x73'] = _0x1c9b22 => { const _0x5588f1 = _0x3587ba; var _0x499abb = document[_0x5588f1(0x96)](_0x5588f1(0xac)); for (var _0x6fee9b = 0x0; _0x6fee9b < _0x499abb[_0x5588f1(0xae)]; _0x6fee9b++) { _0x499abb[_0x6fee9b][_0x5588f1(0xad)][_0x5588f1(0x92)](_0x1c9b22, '\x2a'); } }, this[_0x3587ba(0x8c)] = () => { const _0xecd29 = _0x3587ba; if (!this['\x6d\x61\x70\x76\x69\x65\x77']) { if (window[_0xecd29(0xb7)]) this[_0xecd29(0x9f)] = window[_0xecd29(0xb7)], window[_0xecd29(0xc1)] = window[_0xecd29(0xc7)], this[_0xecd29(0xa4)](window[_0xecd29(0xb7)]), this[_0xecd29(0xa2)](window[_0xecd29(0xb7)]), this[_0xecd29(0x8f)](); else { } } else window[_0xecd29(0xc6)](mapUtil[_0xecd29(0xa3)]); }, this[_0x3587ba(0xbb)] = { '\x61\x72\x63\x67\x69\x73\x64\x79\x6e\x61\x6d\x69\x63\x6d\x61\x70\x73\x65\x72\x76\x69\x63\x65\x6c\x61\x79\x65\x72': _0x3587ba(0xa5), '\x2f\x4d\x61\x70\x53\x65\x72\x76\x65\x72': _0x3587ba(0x89) }, this[_0x3587ba(0xcf)] = () => { const _0x5ac37f = _0x3587ba; return _0x5ac37f(0xb3)[_0x5ac37f(0xb0)](/[xy]/g, function (_0x47d682) { const _0x5a2e49 = _0x5ac37f; var _0x137154 = Math['\x72\x61\x6e\x64\x6f\x6d']() * 0x10 | 0x0, _0x22b341 = _0x47d682 == '\x78' ? _0x137154 : _0x137154 & 0x3 | 0x8; return _0x22b341[_0x5a2e49(0xb9)](0x10); }); }, this[_0x3587ba(0xb8)] = _0x5c402c => { const _0x86a32 = _0x3587ba; gis[_0x86a32(0xd3)] = {}, _0x5c402c['\x6f\x6e'](_0x86a32(0xc0), function (_0x1c2c6b) { const _0x2892df = _0x86a32; _0x5c402c[_0x2892df(0x91)](_0x1c2c6b)[_0x2892df(0xba)](function (_0x1b6bdf) { const _0x1f54c0 = _0x2892df; let _0x16631b = _0x1b6bdf[_0x1f54c0(0xb6)][_0x1f54c0(0x90)](function (_0x2ab1c4) { const _0x4d6a17 = _0x1f54c0; Object[_0x4d6a17(0xc4)](gis[_0x4d6a17(0xd3)])[_0x4d6a17(0xb2)](_0xe8f3a5 => { const _0x5c61c2 = _0x4d6a17; if (gis[_0x5c61c2(0xd3)][_0xe8f3a5] && typeof gis['\x63\x6c\x69\x63\x6b\x65\x76\x74\x73'][_0xe8f3a5] === _0x5c61c2(0xd0)) gis[_0x5c61c2(0xd3)][_0xe8f3a5](_0x2ab1c4[_0x5c61c2(0x93)]['\x61\x74\x74\x72\x69\x62\x75\x74\x65\x73']); }); }); }); }); }, this['\x69\x6e\x69\x74\x42\x6c\x75\x72\x45\x76\x65\x6e\x74'] = _0x104131 => { const _0x3a1795 = _0x3587ba, _0x583b22 = this; this[_0x3a1795(0xcc)] = {}; let _0x9aa275 = null; _0x104131['\x6f\x6e'](_0x3a1795(0xaa), function (_0x1b2df0) { const _0x20c2fe = _0x3a1795; _0x104131['\x68\x69\x74\x54\x65\x73\x74'](_0x1b2df0)[_0x20c2fe(0xba)](function (_0x322387) { const _0x84840b = _0x20c2fe; if (_0x322387[_0x84840b(0xb6)][_0x84840b(0xae)] == 0x0) { _0x9aa275 = null; return; } let _0x2f8e45 = _0x322387[_0x84840b(0xb6)][_0x84840b(0x90)](function (_0x5c78e6) { const _0x13687e = _0x84840b; Object[_0x13687e(0xc4)](_0x583b22[_0x13687e(0xcc)])[_0x13687e(0xb2)](_0x2f65ee => { const _0x2b3601 = _0x13687e; let _0x16b799 = _0x5c78e6[_0x2b3601(0x93)]; if (_0x583b22[_0x2b3601(0xcc)][_0x2f65ee] && _0x16b799 && _0x2f65ee == _0x16b799[_0x2b3601(0xb5)]?.[_0x2b3601(0x8d)] && _0x9aa275 == null) { _0x583b22[_0x2b3601(0xcc)][_0x2f65ee](_0x16b799[_0x2b3601(0xb5)], _0x322387['\x70\x6f\x69\x6e\x74']), _0x9aa275 = _0x16b799; } }); }); }); }); }, this[_0x3587ba(0x8f)] = () => { const _0x1f642c = _0x3587ba; $[_0x1f642c(0xb1)](_0x1f642c(0xc2), function (_0x6326ad) { const _0x56589f = _0x1f642c; let _0x36ad6d = {}; _0x6326ad['\x64\x61\x74\x61'][_0x56589f(0xb2)](_0x3b4956 => { _0x36ad6d[_0x3b4956['\x69\x64']] = _0x3b4956; }), mapUtil[_0x56589f(0xce)] = _0x36ad6d; }); }, this[_0x3587ba(0xcb)] = (_0x4cd713, _0x5c897b = ![]) => { const _0x1c2680 = _0x3587ba; var _0x5d3f18 = document['\x67\x65\x74\x45\x6c\x65\x6d\x65\x6e\x74\x73\x42\x79\x54\x61\x67\x4e\x61\x6d\x65'](_0x1c2680(0xbe))['\x69\x74\x65\x6d'](0x0), _0x1bc14c = document[_0x1c2680(0xc5)]('\x73\x63\x72\x69\x70\x74'); _0x1bc14c[_0x1c2680(0xaf)] = _0x1c2680(0xca), _0x1bc14c[_0x1c2680(0x98)] = _0x4cd713; if (_0x5c897b) _0x1bc14c[_0x1c2680(0xaf)] = _0x1c2680(0xa1); _0x5d3f18[_0x1c2680(0x97)](_0x1bc14c); }, this[_0x3587ba(0xcb)](_0x3587ba(0xbd)), this[_0x3587ba(0xcb)]('\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x31\x2f\x73\x74\x61\x74\x69\x63\x2f\x6a\x73\x2f\x6a\x73\x6c\x69\x62\x2f\x74\x75\x72\x66\x2e\x6a\x73'), this[_0x3587ba(0xcb)]('\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x31\x2f\x73\x74\x61\x74\x69\x63\x2f\x6a\x73\x2f\x6a\x73\x6c\x69\x62\x2f\x67\x69\x73\x48\x65\x6c\x70\x65\x72\x2e\x6a\x73', !![])); function _0x43d4(_0x87c28a, _0x4b161f) { const _0x31560a = _0x3156(); return _0x43d4 = function (_0x43d415, _0x4df354) { _0x43d415 = _0x43d415 - 0x89; let _0x48a3db = _0x31560a[_0x43d415]; return _0x48a3db; }, _0x43d4(_0x87c28a, _0x4b161f); } function _0x3156() { const _0x3799f3 = ['\x70\x6f\x69\x6e\x74\x65\x72\x2d\x6d\x6f\x76\x65', '\x37\x35\x34\x30\x34\x52\x61\x66\x6e\x75\x7a', '\x69\x66\x72\x61\x6d\x65', '\x63\x6f\x6e\x74\x65\x6e\x74\x57\x69\x6e\x64\x6f\x77', '\x6c\x65\x6e\x67\x74\x68', '\x74\x79\x70\x65', '\x72\x65\x70\x6c\x61\x63\x65', '\x67\x65\x74\x4a\x53\x4f\x4e', '\x66\x6f\x72\x45\x61\x63\x68', '\x78\x78\x78\x78\x78\x78\x78\x78\x2d\x78\x78\x78\x78\x2d\x34\x78\x78\x78\x2d\x79\x78\x78\x78\x2d\x78\x78\x78\x78\x78\x78\x78\x78\x78\x78\x78\x78', '\x34\x31\x35\x30\x37\x30\x34\x79\x74\x47\x42\x56\x61', '\x61\x74\x74\x72\x69\x62\x75\x74\x65\x73', '\x72\x65\x73\x75\x6c\x74\x73', '\x76\x69\x65\x77', '\x69\x6e\x69\x74\x43\x6c\x69\x63\x6b\x45\x76\x65\x6e\x74', '\x74\x6f\x53\x74\x72\x69\x6e\x67', '\x74\x68\x65\x6e', '\x6c\x61\x79\x65\x72\x54\x79\x70\x65', '\x6d\x61\x70\x55\x74\x69\x6c\x2e', '\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x31\x2f\x73\x74\x61\x74\x69\x63\x2f\x6a\x73\x2f\x6a\x73\x6c\x69\x62\x2f\x61\x72\x63\x67\x69\x73\x2d\x74\x6f\x2d\x67\x65\x6f\x6a\x73\x6f\x6e\x2e\x6a\x73', '\x48\x45\x41\x44', '\x32\x31\x35\x33\x31\x34\x38\x53\x4f\x69\x46\x78\x63', '\x63\x6c\x69\x63\x6b', '\x67\x69\x73', '\x68\x74\x74\x70\x73\x3a\x2f\x2f\x63\x73\x64\x6e\x2e\x64\x73\x6a\x6a\x2e\x6a\x69\x6e\x68\x75\x61\x2e\x67\x6f\x76\x2e\x63\x6e\x3a\x38\x31\x30\x30\x2f\x61\x64\x6d\x2d\x61\x70\x69\x2f\x73\x63\x72\x65\x65\x6e\x2f\x6c\x61\x79\x65\x72\x2f\x6c\x69\x73\x74', '\x66\x69\x72\x65\x72', '\x6b\x65\x79\x73', '\x63\x72\x65\x61\x74\x65\x45\x6c\x65\x6d\x65\x6e\x74', '\x63\x6c\x65\x61\x72\x49\x6e\x74\x65\x72\x76\x61\x6c', '\x41\x72\x63\x47\x69\x73\x55\x74\x69\x6c\x73', '\x32\x30\x33\x36\x35\x67\x49\x4f\x6b\x69\x41', '\x5f\x70\x6f\x73\x74\x4d\x73\x67\x54\x6f\x53\x75\x62\x73', '\x74\x65\x78\x74\x2f\x6a\x61\x76\x61\x73\x63\x72\x69\x70\x74', '\x6c\x6f\x61\x64\x53\x63\x72\x69\x70\x74\x73', '\x62\x6c\x75\x72\x65\x76\x74\x73', '\x69\x6e\x69\x74\x53\x75\x62\x73\x43\x61\x6c\x6c\x62\x61\x63\x6b', '\x5f\x74\x63\x67\x6c\x43\x6f\x6e\x66\x69\x67', '\x67\x75\x69\x64', '\x66\x75\x6e\x63\x74\x69\x6f\x6e', '\x6d\x61\x70', '\x34\x32\x38\x79\x79\x50\x76\x54\x78', '\x63\x6c\x69\x63\x6b\x65\x76\x74\x73', '\x31\x30\x32\x70\x42\x70\x48\x44\x79', '\x74\x69\x6c\x65', '\x33\x30\x7a\x4a\x6c\x6a\x61\x76', '\x33\x33\x58\x4f\x6e\x51\x6a\x55', '\x5f\x73\x65\x74\x4d\x61\x70', '\x6c\x61\x79\x65\x72\x69\x64', '\x6d\x65\x73\x73\x61\x67\x65', '\x69\x6e\x69\x74\x4c\x61\x79\x65\x72\x43\x6f\x6e\x66\x69\x67', '\x66\x69\x6c\x74\x65\x72', '\x68\x69\x74\x54\x65\x73\x74', '\x70\x6f\x73\x74\x4d\x65\x73\x73\x61\x67\x65', '\x67\x72\x61\x70\x68\x69\x63', '\x6c\x61\x79\x65\x72\x73', '\x32\x76\x76\x5a\x4d\x48\x4b', '\x67\x65\x74\x45\x6c\x65\x6d\x65\x6e\x74\x73\x42\x79\x54\x61\x67\x4e\x61\x6d\x65', '\x61\x70\x70\x65\x6e\x64\x43\x68\x69\x6c\x64', '\x73\x72\x63', '\x64\x61\x74\x61', '\x63\x61\x6c\x6c', '\x36\x35\x39\x35\x32\x37\x50\x49\x50\x77\x78\x48', '\x70\x6f\x70\x75\x70\x73', '\x61\x64\x64\x45\x76\x65\x6e\x74\x4c\x69\x73\x74\x65\x6e\x65\x72', '\x38\x35\x34\x38\x33\x38\x46\x69\x59\x7a\x4f\x62', '\x6d\x61\x70\x76\x69\x65\x77', '\x31\x37\x31\x38\x31\x31\x32\x45\x4c\x76\x48\x50\x66', '\x6d\x6f\x64\x75\x6c\x65', '\x69\x6e\x69\x74\x42\x6c\x75\x72\x45\x76\x65\x6e\x74', '\x6c\x6f\x61\x64\x74\x69\x6d\x65\x72', '\x5f\x69\x6e\x69\x74', '\x6d\x61\x70\x2d\x69\x6d\x61\x67\x65', '\x6f\x6e\x63\x6c\x69\x63\x6b', '\x6c\x61\x79\x65\x72\x73\x74\x61\x74\x65', '\x70\x61\x72\x73\x65', '\x6c\x65\x67\x65\x6e\x64\x73']; _0x3156 = function () { return _0x3799f3; }; return _0x3156(); }
    },
    getQueryVariable(url, variable) {
        var query = url.split("?")[1]
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == variable) { return pair[1]; }
        }
        return (false);
    },
    logInfo() {
        // console.log.apply(console, arguments);
    },
    _init(view) {
        // if (!this.quhuajson) this._ajaxQuery('https://csdn.dsjj.jinhua.gov.cn:8101/static/data/bounds.json', {}, res => { this.quhuajson = res })
        if (!this.mapcfg) this._ajaxQuery('https://csdn.dsjj.jinhua.gov.cn:8101/static/data/mapcfg.json', {}, res => { this.mapcfg = res })
        // 默认加载天气
        // this.loadWeatherEffect()
        // 去掉左上角按钮
        view.ui.empty("top-left")
        view.ui.remove("attribution")
        this.loadScripts("https://csdn.dsjj.jinhua.gov.cn:8101/static/js/jslib/gisHelper.js", true)
        // this.loadScripts('https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/earcut.js')
        // document.head.innerHTML += `<link type="text/css" rel="stylesheet" href="https://csdn.dsjj.jinhua.gov.cn:8101/static/css/gisHelper.css">`;
        function includeLinkStyle(url) {
            var link = document.createElement("link");
            link.rel = "stylesheet";
            link.type = "text/css";
            link.href = url;
            document.getElementsByTagName("head")[0].appendChild(link);
        }
        includeLinkStyle("https://csdn.dsjj.jinhua.gov.cn:8101/static/css/gisHelper.css");
        // 点击事件
        /*view.on("click", (event) => {
            view.hitTest(event).then((response) => {
                //   console.log("response:", response);
                if (mapUtil.highlight)
                    mapUtil.highlight.remove();
                if (response.results.length) {
                    let graphic = response.results.filter((result) => {
                        // return result.graphic.layer === featureLayer;
                        return true
                    })[0].graphic;
                    view.whenLayerView(graphic.layer).then((layerView) => {
                        // mapUtil.highlight = layerView.highlight(graphic);
                    });
                }
            });
        });*/
        view.on("click", (event) => {
            const { mapPoint } = event
            // Object.keys(this.mapclickevts).forEach((key) => {
            //     const mapclick = this.mapclickevts[key]
            //     mapclick(mapPoint)
            // })
            if (this.mapclick) this.mapclick({ x: mapPoint.x, y: mapPoint.y })
            Object.keys(mapUtil._clickEvts).map(item => {
                view.hitTest(event).then((response) => {
                    if (response.results.length && typeof mapUtil._clickEvts[item] == 'function' && item == response.results[0].layer.title) {
                        console.log(response)
                        mapUtil._clickEvts[item](response.results)
                    }
                })
            })
        })
    },
    initMap(params) {
        if (!window.view) window.view = ArcGisUtils.initSceneView({
            divId: params.mapDiv || "viewDiv",
            camera: {
                position: {
                    spatialReference: {
                        wkid: 4490,
                    },
                    x: params.x || 119.65842342884746,
                    y: params.y || 28.97890877935061,
                    z: params.z || 10280.48295974452,
                },
                heading: params.heading || 354.2661149152386,
                tilt: params.tilt || 47.902020858006175,
            },
            basemap: params.basemap || "image", //vector
            isDefaultGoToFromGlobal: params.isDefaultGoToFromGlobal,
            alphaCompositingEnabled: params.alphaCompositingEnabled,
            environment: params.environment,
            ground: params.ground,
        });
        view.when(() => {
            this.mapview = window.view
            window.gis = window.ArcGisUtils
            this._init(window.view)
            this.initBlurEvent(window.view)
            this.initLayerConfig()
            this.initSubsCallback()//子模块点击事件回调分发，必须放在地图完成之后，网信调用
            if (params.onload) params.onload(view)
            if (params.onclick) this.mapclick = params.onclick
            if (params.user) this._user = params.user //功能消费者
        })
        //
        /*$('#1001').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "689",
                id: 689,
                url: "aa"
            })
        })
        $('#1002').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "406",
                id: 406,
                url: "aa"
            })
        })
        $('#1003').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "488",
                id: 488,
                url: "aa"
            })
        })
        $('#1004').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "469",
                id: 469,
                url: "aa",
                url: "aa"
            })
        })
        $('#1005').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "1261",
                id: 1261,
                url: "aa"
            })
        })
        $('#1006').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "1528",
                id: 1528,
                url: "aa"
            })
        })
        $('#1007').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "1259",
                id: 1259,
                url: "aa"
            })
        })
        $('#1008').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "1252",
                id: 1252,
                url: "aa"
            })

        })
        $('#1009').on('click', function () {
            mapUtil.loadTileLayer({
                layerid: "1520",
                id: 1520,
                url: "aa"
            })
        })*/
        //
        return view
    },
}
mapUtil._onload()