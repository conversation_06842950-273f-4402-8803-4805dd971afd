<template>
  <div class="container">
    <div class="left">
      <left></left>
    </div>
    <div class="center">
      <center></center>
    </div>
    <div class="right">
      <right></right>
    </div>
  </div>
</template>

<script>
import {} from '@/api/home'
import left from '@/pages/home/<USER>/left/index.vue'
import right from '@/pages/home/<USER>/right/index.vue'
import center from '@/pages/home/<USER>/center/index.vue'
export default {
  name: 'index',
  components: { left, right, center },
  data() {
    return {}
  },
  mounted() {
    this.$nextTick(() => {})
  },
  computed: {},
  methods: {},
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  justify-content: space-between;
  .left {
    margin-left: 10px;
  }
  .center {
    margin: 0 10px;
  }
  .right {
    margin-right: 10px;
  }
}
</style>
