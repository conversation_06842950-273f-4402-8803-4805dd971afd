<template>
  <div class="firstTitle">
    <div class="title">{{ title }}</div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.firstTitle {
  width: 480px;
  height: 40px;
  background: url('@/assets/img/title_bg.png') no-repeat;
  background-size: 100% 100%;
  margin-top: 20px;
  position: relative;
  .title {
    position: absolute;
    left: 36px;
    bottom: 6px;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 36px;
    letter-spacing: 1px;
    // text-shadow: 0px 5px 10px rgba(0, 0, 0, 0.4);
    text-align: left;
    background: linear-gradient(180deg, #ffffff 0%, #b9ebff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>