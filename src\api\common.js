import { request, request2 } from '@/utils/request'

// 获取用户信息
export const getInfo = () => {
  return request2(`/jazz-api/getInfo`, 'get')
}

// 今日值班
export const getDutyToday = () => {
  return request2(`/jazz-api/bigScreen/page/dutyToday`, 'get')
}

// 今日值班本周列表
export const getDutyTodayWeek = (params) => {
  return request2(`/jazz-api/bigScreen/page/dutyTodayWeek`, 'get', params)
}

// 是否市本级
export const getIsJh = () => {
  return request2(`/jazz-api/bigScreen/page/isJh`, 'get')
}

// 获取所有应用列表
export const listAllYy = () => {
  return request(`/tyywpt/tTyywYy/listAll`, 'get')
}

// 查询部门下拉树结构
export const deptTreeSelect = () => {
  return request(`/system/user/deptTreeNew`, 'get')
}
