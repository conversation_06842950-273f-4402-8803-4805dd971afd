<template>
  <div class="map">
    <div id="container"></div>

    <img class="spjk" src="@/assets/img/common/spjk.png" alt="" @click="spjkClick()" />
    <img v-show="spjkShow" class="spjk-icon" src="@/assets/img/common/spjk-icon.png" alt="">
    <img class="zlimg" src="@/assets/img/common/zhiling.png" alt="" @click="showDialog" />
    <div class="radioDiv">
      <div class="radioitem" v-for="(item, i) in radioArr" :key="i + 'a'" @click="clickRadio(i)">
        <div :class="{ 'radioActive': radioIndex == i }">{{ radioIndex == i ? "√" : "" }}</div>
        <div>{{ item }}</div>
      </div>
    </div>
    <!-- 指挥调度弹框 -->
    <CommonDialog :dialogFlag="dialogFlag" :title="'指挥调度'" :dialogWidth="'1040px'" :selectFlag="true"
      @close="dialogFlag = false" :options="options" :tabArrFlag="true" :tabArr="[{name:'本日',value:1}, {name:'本周',value:2},
    {name:'本月',value:3},{name:'全部',value:4},]"
      @change-county="changeCountyFn" @tab-active="tabActive"  >
      <zhddDialog @close="dialogFlag = false" :dialogFlag="dialogFlag" :changeCounty="changeCounty" :tabActiveName = "tabActiveName"></zhddDialog>
    </CommonDialog>
  </div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader';
import CommonDialog from '@/components/Commondialog/index.vue'
import zhddDialog from "../../pages/home/<USER>/commandAndDispatch.vue"
export default {
  name: 'gaodeMap',
  components: {
    CommonDialog,
    zhddDialog
  },
  data() {
    return {
      map: "", // 地图
      pointSimplifier: null,  //点位
      district: null, //区划
      numbers: [
        { name: "金华市", value: 599, ranking: 1 },
        { name: "永康市", value: 1167, ranking: 1 },
        { name: "东阳市", value: 1288, ranking: 1 },
        { name: "武义县", value: 1134, ranking: 1 },
        { name: "兰溪市", value: 1267, ranking: 2 },
        { name: "磐安县", value: 1022, ranking: 2 },
        { name: "金东区", value: 1543, ranking: 2 },
        { name: "浦江县", value: 1039, ranking: 3 },
        { name: "婺城区", value: 1331, ranking: 3 },
        { name: "义乌市", value: 1589, ranking: 3 },
      ],

      dialogFlag: false,
      showMapSrc: "",
      radioArr: ["底库人员", "重点群体", "重大事件", "执行评分"],
      radioIndex: 0,
      options: [
        {
          "label": "金华市",
          "value": 0
        },
        {
          "label": "婺城区",
          "value": 1
        },
        {
          "label": "金东区",
          "value": 2
        },
        {
          "label": "武义县",
          "value": 3
        },
        {
          "label": "浦江县",
          "value": 4
        },
        {
          "label": "磐安县",
          "value": 5
        },
        {
          "label": "兰溪市",
          "value": 6
        },
        {
          "label": "义乌市",
          "value": 7
        },
        {
          "label": "东阳市",
          "value": 8
        },
        {
          "label": "永康市",
          "value": 9
        }
      ],
      spjkShow: false,
      changeCounty: '金华市',
      tabActiveName:1,
      typeIndex:0,
    }
  },
  mounted() {
    this.initMap()

  },
  methods: {
    //初始化地图
    initMap() {
      window._AMapSecurityConfig = { securityJsCode: 'ffc7f18208a056dd2ff9077c78928730' };
      this.$nextTick(() => {
        AMapLoader.load({
          key: "951ac3dcc6cf83f6832f50cf3e9f44f9",             // 申请好的Web端开发者Key，首次调用 load 时必填
          version: "2.0",      // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
          plugins: ['AMap.DistrictSearch'],       // 需要使用的的插件列表，如比例尺'AMap.Scale'等
          AMapUI: {
            version: '1.1',
            plugins: ['geo/DistrictCluster', 'misc/PointSimplifier']
          }
        }).then((AMap) => {
          this.map = new AMap.Map("container", {  //设置地图容器id
            viewMode: "3D",    //是否为3D地图模式
            zoom: 9.75,           //初始化地图级别
            zooms: [9.75, 20],   //控制缩放范围
            center: [120.038634, 29.122511], //初始化地图中心点位置
            mapStyle: "amap://styles/80dd80c95d79d09095eb5fbef5cfaef1",
            terrain: true
          });
          this.showJinhua()
          const that = this
          AMapUI.load(['ui/geo/DistrictCluster', 'ui/misc/PointSimplifier', 'lib/$', 'lib/utils'], function (DistrictCluster, PointSimplifier, $, utils) {
            that.showJinhuaPoint(PointSimplifier)
            that.showJinhuaArea(DistrictCluster, $, utils)
          });
        }).catch(e => {
          console.log(e);
        });
      })
    },
    //设置只显示金华板块
    showJinhua() {
      let options = {
        subdistrict: 1,
        extensions: "all",
        level: "district",
      };
      let district = new AMap.DistrictSearch(options);
      district.search("金华市", (status, result) => {
        let outer = [
          new AMap.LngLat(-360, 90, true),
          new AMap.LngLat(-360, -90, true),
          new AMap.LngLat(360, -90, true),
          new AMap.LngLat(360, 90, true),
        ];
        let holes = result.districtList[0].boundaries;

        let pathArray = [outer];
        pathArray.push.apply(pathArray, holes);
        let polygon = new AMap.Polygon({
          pathL: pathArray,
          //线条颜色，使用16进制颜色代码赋值。默认值为#006600
          strokeColor: "rgb(20,164,173)",
          strokeWeight: 4,
          //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          strokeOpacity: 0.5,
          //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
          fillColor: "#000f37",
          //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          fillOpacity: 1,
          //轮廓线样式，实线:solid，虚线:dashed
          strokeStyle: "solid",
          /*勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在
          ie9+浏览器有效 取值：
          实线：[0,0,0]
          虚线：[10,10] ，[10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线
          点画线：[10,2,10]， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实
          线和10个像素的空白 （如此反复）组成的虚线*/
          strokeDasharray: [10, 2, 10],
        });
        polygon.setPath(pathArray);
        this.map.add(polygon);
      });
    },
    //设置金华区划聚合
    showJinhuaArea(DistrictCluster, $, utils) {
      const that = this
      function MyRender(distClusterIns, opts) {
        //直接调用父类的构造函数
        MyRender.__super__.constructor.apply(this, arguments);
      }
      //继承默认引擎
      utils.inherit(MyRender, DistrictCluster.Render.Default);
      utils.extend(MyRender.prototype, {
        drawFeaturePolygons(ctx, polygons, styleOptions, feature, dataItems) {
          //调用父类方法
          MyRender.__super__.drawFeaturePolygons.apply(this, arguments);
          //直接绘制聚合信息
          this.drawMyLabel(feature, dataItems);
        },
        _initContainter() {
          //调用父类方法
          MyRender.__super__._initContainter.apply(this, arguments);
          //创建一个新的canvas
          this._createCanvas('mylabel', this._container);
        },
        /**
         * 绘制一个label替代聚合标注
         */
        drawMyLabel(feature, dataItems) {
          let pixelRatio = this.getPixelRatio() //高清下存在比例放大
          let containerPos = that.map.lngLatToContainer(feature.properties.centroid || feature.properties.center)
          let labelCtx = this._getCanvasCxt('mylabel')
          //文字的中心点
          let centerX = containerPos.getX() * pixelRatio,
            centerY = containerPos.getY() * pixelRatio
          labelCtx.save()
          let item = that.getNumber(that.numbers, feature.properties.name)
          let color = ''
          switch (item.ranking) {
            case 1:
              color = '#FF6D6D'
              break
            case 2:
              color = '#33BEFF'
              break
            case 3:
              color = '#F5CC53'
              break
          }
          let text = that.getNumber(that.numbers, feature.properties.name).value
          let text2 = feature.properties.name
          let unit = ''
          switch (that.radioIndex) {
            case 0:
              unit = '人'
              break
            case 1:
              unit = '个'
              break
            case 2:
              unit = '件'
              break
            case 3:
              unit = '分'
              break
          }

          //背景
          labelCtx.fillStyle = 'transparent'

          //分数
          labelCtx.fillStyle = color
          labelCtx.textBaseline = 'middle'
          labelCtx.font = 32 * pixelRatio + 'px Source Han Sans CN-Bold, Source Han Sans CN'
          let textMetrics1 = labelCtx.measureText(text)
          let halfTxtWidth1 = textMetrics1.width / 2
          labelCtx.fillText(text, centerX - halfTxtWidth1, centerY)

          //区划名
          labelCtx.font = 16 * pixelRatio + 'px Source Han Sans CN-Bold, Source Han Sans CN'
          labelCtx.fillStyle = '#fff'
          let textMetrics2 = labelCtx.measureText(text2)
          let halfTxtWidth2 = textMetrics2.width / 2
          labelCtx.fillText(text2, centerX - halfTxtWidth2 + 6.5, centerY + 30)

          //单位
          labelCtx.font = 14 * pixelRatio + 'px Source Han Sans CN-Bold, Source Han Sans CN'
          labelCtx.fillStyle = color
          let textMetricsunit = labelCtx.measureText(unit)
          let halfTxtWidthunit = textMetricsunit.width / 2
          labelCtx.fillText(unit, centerX + halfTxtWidth1 + halfTxtWidthunit - 6, centerY + 4)
        },
      });


      // 定义行政区划聚合实例
      this.district = new DistrictCluster({
        map: this.map, //所属的地图实例
        zIndex: 11,
        topAdcodes: [330700],
        autoSetFitView: false,
        getPosition(item) {
          if (!item) {
            return null;
          }
          let parts = item.split(',');
          //返回经纬度
          return [parseFloat(parts[0]), parseFloat(parts[1])];
        },
        renderConstructor: MyRender,
        renderOptions: {
          // 区划面的基本样式。
          featureStyle: {
            fillStyle: "#43d0ff", //填充色
            lineWidth: 2, //描边线宽
            strokeStyle: "#43d0ff", //描边色
            //鼠标Hover后的样式
            hoverOptions: {
              fillStyle: "rgba(61, 221, 255, 0.6)",
            },
          },
          // 返回聚合信息标注
          getClusterMarker: null,
          // getClusterMarker(feature, dataItems, recycledMarker) {
          //   //label内容
          //   let item = that.getNumber(that.numbers,feature.properties.name)
          //   let content = ""
          //   let content1 =
          //     `
          //       <div class="number redlinear"> ${item.value} <span class="unit">分</span> </div>
          //       <div class="mark-label"> ${feature.properties.name} </div>
          //     `;
          //   let content2 =
          //     `
          //       <div class="number bluelinear"> ${item.value} <span class="unit">分</span> </div>
          //       <div class="mark-label"> ${feature.properties.name} </div>
          //     `;
          //   let content3 =
          //     `
          //       <div class="number yellowlinear"> ${item.value} <span class="unit">分</span> </div>
          //       <div class="mark-label"> ${feature.properties.name} </div>
          //     `;
          //   switch (item.ranking) {
          //     case 1:
          //       content = content1
          //       break;
          //     case 2:
          //       content = content2
          //       break;
          //     case 3:
          //       content = content3
          //       break;
          //   }
          //   let label = {
          //     offset: new AMap.Pixel(-37, 0), //修改label相对于marker的位置
          //     content: content,
          //   };
          //
          //   //存在可回收利用的marker
          //   if (recycledMarker) {
          //     //直接更新内容返回
          //     recycledMarker.setLabel(label);
          //     return recycledMarker;
          //   }
          //
          //   //返回一个新的Marker
          //   return new AMap.Marker({
          //     label: label,
          //   });
          // },
        },
      });
      this.setMapData()
    },
    //设置打点
    showJinhuaPoint(PointSimplifier) {
      this.pointSimplifier = new PointSimplifier({
        map: this.map, //所属的地图实例
        autoSetFitView: false, //禁止自动更新地图视野
        zIndex: 110,
        getPosition: function (item) {
          if (!item) {
            return null;
          }
          let parts = item.split(',');
          //返回经纬度
          return [parseFloat(parts[0]), parseFloat(parts[1])];
        },
        getHoverTitle: function (dataItem, idx) {
          return idx + ': ' + dataItem;
        },
        renderOptions: {
          //点的样式
          pointStyle: {
            width: 6,
            height: 6,
            fillStyle: 'rgba(153, 0, 153, 0.38)'
          },
          //鼠标hover时的title信息
          hoverTitleStyle: {
            position: 'top'
          }
        }
      });
    },
    //设置点位数据
    setMapData() {
      const mapData = [
        "113.864691,22.942327",
        "120.412618,36.382612",
        "113.370643,22.938827",
        "113.001181,23.120518",
        "112.985037,23.15046",
        "113.890205,22.798043",
        "110.361899,20.026695",
        "113.682288,34.618975",
        "121.434529,31.215641",
        "109.488707,18.309754",
        "120.682502,28.011099",
        "120.68556,30.912366",
        "126.687123,45.787618",
        "120.48506,30.053066",
        "103.970724,30.397931",
        "117.270073,37.96162",
        "117.212164,31.831641",
        "120.256218,31.882242",
        "121.411101,31.059407",
        "113.336586,33.729581",
        "104.137953,30.784276",
        "114.141516,23.159282",
        "120.499683,30.042305",
        "120.487242,32.180365",
        "108.94686,34.362975",
        "121.299895,31.105064",
        "112.873295,22.920901",
        "114.164329,22.644532",
        "113.373916,23.086728",
        "120.282954,30.196059",
        "113.250159,23.075847",
        "121.145445,31.193621",
        "116.675933,39.986343",
        "120.896422,31.472813",
        "120.75997,31.734934",
        "118.782607,32.00381",
        "118.314741,32.26999",
        "105.268729,23.732875",
        "111.723311,34.771838",
        "120.169746,33.357616",
        "119.537126,26.200017",
        "119.953287,37.165859",
        "113.830123,23.00734",
        "100.70191,25.408898",
        "119.273795,26.060002",
        "121.373427,31.188567",
        "116.466752,39.951042",
      ];
      this.pointSimplifier.setData(mapData);
      this.district.setData(mapData);
    },
    //获取区划对应的数值
    getNumber(arr, name) {
      let result = ""
      arr.forEach((item, i) => {
        if (item.name == name) {
          result = item
        }
      })
      return result
    },

    //指令弹窗
    showDialog() {
      this.dialogFlag = true
    },
    clickRadio(i) {
      this.radioIndex = i
      switch (i) {
        case 0:
          this.numbers = [
            { name: "金华市", value: 599, ranking: 1 },
            { name: "义乌市", value: 1589, ranking: 1 },
            { name: "金东区", value: 1543, ranking: 1 },
            { name: "婺城区", value: 1331, ranking: 1 },
            { name: "东阳市", value: 1288, ranking: 2 },
            { name: "兰溪市", value: 1267, ranking: 2 },
            { name: "永康市", value: 1167, ranking: 2 },
            { name: "武义县", value: 1134, ranking: 3 },
            { name: "浦江县", value: 1039, ranking: 3 },
            { name: "磐安县", value: 1022, ranking: 3 },
          ]
          break;
        case 1:
          this.numbers = [
            { name: "金华市", value: 599, ranking: 1 },
            { name: "金东区", value: 10, ranking: 1 },
            { name: "浦江县", value: 9, ranking: 1 },
            { name: "义乌市", value: 8, ranking: 1 },
            { name: "婺城区", value: 7, ranking: 2 },
            { name: "东阳市", value: 7, ranking: 2 },
            { name: "武义县", value: 7, ranking: 2 },
            { name: "永康市", value: 6, ranking: 3 },
            { name: "磐安县", value: 6, ranking: 3 },
            { name: "兰溪市", value: 5, ranking: 3 },
          ]
          break;
        case 2:
          this.numbers = [
            { name: "金华市", value: 599, ranking: 1 },
            { name: "义乌市", value: 9, ranking: 1 },
            { name: "金东区", value: 9, ranking: 1 },
            { name: "浦江县", value: 9, ranking: 1 },
            { name: "东阳市", value: 7, ranking: 2 },
            { name: "婺城区", value: 7, ranking: 2 },
            { name: "永康市", value: 6, ranking: 2 },
            { name: "武义县", value: 6, ranking: 3 },
            { name: "磐安县", value: 6, ranking: 3 },
            { name: "兰溪市", value: 5, ranking: 3 },
          ]
          break;
        case 3:
          this.numbers = [
            { name: "金华市", value: 599, ranking: 1 },
            { name: "永康市", value: 98, ranking: 1 },
            { name: "金东区", value: 97, ranking: 1 },
            { name: "武义县", value: 96, ranking: 1 },
            { name: "义乌市", value: 94, ranking: 2 },
            { name: "东阳市", value: 92, ranking: 2 },
            { name: "婺城区", value: 92, ranking: 2 },
            { name: "兰溪市", value: 91, ranking: 3 },
            { name: "磐安县", value: 89, ranking: 3 },
            { name: "浦江县", value: 88, ranking: 3 },
          ]
          break;
      }
    },
    // 视频监控点击
    spjkClick() {
      this.spjkShow = !this.spjkShow
    },
    changeCountyFn(val) {
      this.changeCounty = val
    },
    tabActive(val){
      this.tabActiveName = val
    },
  },
  watch: {
    numbers() {
      this.setMapData()
    }
  }
}
</script>

<style scoped lang="less">
.map {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;

  .zlimg {
    width: 227px;
    height: 227px;
    position: absolute;
    right: 0;
    bottom: 0;
    cursor: pointer;
  }

  .spjk {
    width: 56px;
    height: 56px;
    position: absolute;
    right: 10px;
    top: 13px;
    cursor: pointer;
  }

  .spjk-icon {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 54px;
    cursor: pointer;
  }

  .radioDiv {
    position: absolute;
    width: 115px;
    height: 140px;
    background-image: url("@/assets/img/common/radioBg.png");
    background-size: cover;
    left: 18px;
    bottom: 14px;
    padding-left: 14px;
    padding-top: 14px;
    box-sizing: border-box;

    .radioitem {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      cursor: pointer;

      >div:first-child {
        width: 14px;
        height: 14px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #FFFFFF;

      }

      >div:nth-child(2) {
        margin-left: 9px;
        font-size: 16px;
        font-family: "DIN-Medium";
        font-weight: 400;
        color: #FFFFFF;
        line-height: 14px;
        -webkit-background-clip: text;
      }
    }


    .radioActive {
      // background: rgba(139, 110, 0, 0.2);
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 178.45999270677567, 0, 1)) 1 1 !important;
      background: linear-gradient(to top, #FFDC00, #fffcf3);
      -webkit-background-clip: text;
      color: transparent;
      text-align: center;
      line-height: 14px;
      font-weight: 700;
      cursor: pointer;
    }
  }

  #container {
    padding: 0;
    margin: 0;
    width: 820px;
    height: 580px;

    /deep/ .amap-marker {
      img {
        display: none;
      }

      .amap-marker-label {
        border: none;
        background-color: unset;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: fit-content;
        line-height: unset;

        .number {
          height: auto;
          font-size: 32px;
          font-family: DIN-Bold, DIN;
          font-weight: bold;

          .unit {
            font-size: 16px;
            margin-left: -5px;
          }
        }

        .redlinear {
          background: linear-gradient(180deg, #FFFFFF 0%, #FF6D6D 99%) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
        }

        .yellowlinear {
          background: linear-gradient(180deg, #FFFFFF 0%, #F5CC53 99%) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
        }

        .bluelinear {
          background: linear-gradient(180deg, #FFFFFF 0%, #33BEFF 99%) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
        }

        .mark-label {
          font-size: 16px;
          font-family: Source Han Sans CN-Bold, Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          -webkit-text-stroke: 1px #0A3274;
          text-stroke: 1px #0A3274;
          -webkit-background-clip: text;
        }
      }
    }
  }
}
</style>
