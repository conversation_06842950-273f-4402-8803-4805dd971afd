<template>
  <div class="right-container">
    <FirstTitle :title="'事件处置情况'"></FirstTitle>
    <sjczqk :dataInfo="dataInfo"></sjczqk>
    <FirstTitle :title="'隐患告警情况'"></FirstTitle>
    <fxyh :fxyhInfo="fxyhInfo" :gjyyList='gjyyList' :gjdjList='gjdjList'></fxyh>
  </div>
</template>

<script>
import FirstTitle from '@/components/FirstTitle/index.vue'
import sjczqk from '@/pages/home/<USER>/right/components/sjczqk.vue'
import fxyh from '@/pages/home/<USER>/right/components/fxyh.vue'
import { getGjxg, getYhgjqk, getGjyyTop5,getGjjjcdfx } from '@/api/home'

export default {
  components: { FirstTitle, sjczqk, fxyh },

  data() {
    return {
      //事件处置情况
      dataInfo: {},
      //隐患告警情况
      fxyhInfo: {},
      //告警应用top5
      gjyyList: [],
      //告警等级统计
      gjdjList:[],
      dateValue: [], // 存储当前选择的日期范围
    }
  },
  mounted() {
    this.init()
    // 监听日期变化事件
    this.$bus.$on('dateChange', this.handleDateChange)
  },
  methods: {
    init() {},
    // 处理日期变化事件
    handleDateChange(dateValue) {
      console.log('left组件接收到日期变化:', dateValue)
      this.dateValue = dateValue
      // 重新调用接口获取数据
      this.getGjxg()
      this.getYhgjqk()
      this.getGjyyTop5()
      this.getGjjjcdfx()
    },
    //事件处置情况
    async getGjxg() {
      // 构建请求参数
      const params = {}

      // 如果有选择日期范围，则添加到参数中
      if (this.dateValue && this.dateValue.length === 2) {
        params.startTime = this.dateValue[0]
        params.endTime = this.dateValue[1]
      } else {
        params.startTime = null
        params.endTime = null
      }

      const res = await getGjxg(params)
      this.dataInfo = res.data.data
    },
    //隐患告警情况
    async getYhgjqk() {
      // 构建请求参数
      const params = {}

      // 如果有选择日期范围，则添加到参数中
      if (this.dateValue && this.dateValue.length === 2) {
        params.startTime = this.dateValue[0]
        params.endTime = this.dateValue[1]
      } else {
        params.startTime = null
      }
      const res = await getYhgjqk(params)
      this.fxyhInfo = res.data.data
    },
    //告警应用top5
    async getGjyyTop5() {
      // 构建请求参数
      const params = {}

      // 如果有选择日期范围，则添加到参数中
      if (this.dateValue && this.dateValue.length === 2) {
        params.startTime = this.dateValue[0]
        params.endTime = this.dateValue[1]
      } else {
        params.startTime = null
      }
      const res = await getGjyyTop5(params)
      this.gjyyList = res.data.data
    },
    //告警等级统计
    async getGjjjcdfx() {
      // 构建请求参数
      const params = {}

      // 如果有选择日期范围，则添加到参数中
      if (this.dateValue && this.dateValue.length === 2) {
        params.startTime = this.dateValue[0]
        params.endTime = this.dateValue[1]
      } else {
        params.startTime = null
      }
      const res = await getGjjjcdfx(params)
      this.gjdjList = res.data.data
    },
  },
}
</script>

<style lang="less" scoped>
.right-container {
  width: 500px;
  height: 950px;
  background-color: #0b13204d;
}
</style>