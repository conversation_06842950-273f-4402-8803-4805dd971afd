<template>
  <div>
    <div class="item">
      <div class="itemList flex-b">
        <div class="liBox flex-b" style="width: 90px">
          <div class="li">
            <div class="key fz14">接入应用数</div>
            <div class="value">{{ yyInfo.yy }}</div>
          </div>
          <img src="@/assets/img/home/<USER>/divider_vertical.png" class="divider-v" />
        </div>
        <div class="liBox flex-b" style="width: 138px">
          <div class="li">
            <div class="key fz14">问题平均处置时长</div>
            <div class="value">1.5h</div>
          </div>
          <img src="@/assets/img/home/<USER>/divider_vertical.png" class="divider-v" />
        </div>
        <div class="liBox flex-b" style="width: 120px">
          <div class="li">
            <div class="key fz14">应用评价健康度</div>
            <div class="value">95%</div>
          </div>
          <img src="@/assets/img/home/<USER>/divider_vertical.png" class="divider-v" />
        </div>
        <div class="liBox flex-b" style="width: 108px">
          <div class="li">
            <div class="key fz14">运维平均分数</div>
            <div class="value">95</div>
          </div>
        </div>
      </div>
    </div>
    <div class="item">
      <div class="flex-b">
        <div class="flex-c">
          <img :src="yzyInfo.icon" class="icon" />
          <div class="name">{{ yzyInfo.name }}</div>
        </div>
        <div class="num">{{ yzyInfo.value }}{{ yzyInfo.unit }}</div>
      </div>
      <img src="@/assets/img/home/<USER>/divider_horizontal.png" class="divider-h" />
      <div class="itemList flex-b">
        <div class="liBox flex-b" v-for="(value, key, j) in yzyInfo.itemList" :key="key">
          <div class="li" :style="{ width: '116px' }">
            <div class="key">{{ key }}</div>
            <div class="value">{{ value }}</div>
          </div>
          <img
            v-if="j !== Object.keys(yzyInfo.itemList).length - 1"
            src="@/assets/img/home/<USER>/divider_vertical.png"
            class="divider-v"
          />
        </div>
      </div>
      <img src="@/assets/img/home/<USER>/divider_horizontal.png" class="divider-h" />
      <div class="itemList">
        <div class="liBox flex-b" v-for="(value, key, j) in yzyInfo.itemList1" :key="key">
          <div class="li" :style="{ width: '116px' }">
            <div class="key">{{ key }}</div>
            <div class="value">{{ value }}</div>
          </div>
          <img
            v-if="j !== Object.keys(yzyInfo.itemList1).length - 1"
            src="@/assets/img/home/<USER>/divider_vertical.png"
            class="divider-v"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      yyInfo: {},
      yzyInfo: {
        name: '云资源总数',
        value: '0',
        unit: '',
        icon: require('@/assets/img/home/<USER>/yzyzs.png'),
        itemList: { ECS: '0', RDS: '0', SLB: '0', OSS: '0' },
        itemList1: { REDIS: '0', polarDbO: '0', polarDbM: '0' },
      },
    }
  },
  watch: {
    dataInfo: {
      handler() {
        this.init()
      },
      immediate: true,
    },
  },
  methods: {
    init() {
      this.yyInfo = this.dataInfo
      this.yzyInfo.value = this.dataInfo.yzyzs
      this.yzyInfo.itemList.ECS = this.dataInfo.ecs
      this.yzyInfo.itemList.RDS = this.dataInfo.rds
      this.yzyInfo.itemList.SLB = this.dataInfo.slb
      this.yzyInfo.itemList.OSS = this.dataInfo.oss
      this.yzyInfo.itemList1.REDIS = this.dataInfo.redis
      this.yzyInfo.itemList1.polarDbO = this.dataInfo.polarDbO
      this.yzyInfo.itemList1.polarDbM = this.dataInfo.polarDb
    },
  },
}
</script>

<style lang="less" scoped>
.item {
  background: linear-gradient(to right, #026ef100, #026ef13b, #026ef100);
  margin: 0 0 16px 0;
  padding: 12px;
  box-sizing: border-box;
  .img {
    width: 25px;
    height: 28px;
  }
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    line-height: 16px;
    text-align: left;
    margin-left: 14px;
  }
  .num {
    width: 80px;
    height: 35px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 24px;
    line-height: 35px;
    text-align: center;
    background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .itemList {
    padding-top: 12px;
    box-sizing: border-box;
    display: flex;
    .liBox {
      // width: 100%;
      // flex: 1;
      .li {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // padding: 0 50px;
        box-sizing: border-box;
        position: relative;
        .key {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 23px;
          text-align: center;
        }
        .fz14 {
          font-size: 14px;
        }
        .value {
          margin-top: 4px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 18px;
          line-height: 26px;
          background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: center;
        }
      }
    }
  }
}
.divider-h {
  width: 460px;
  height: 1px;
  display: block;
}
.divider-v {
  width: 1px;
  height: 60px;
  display: block;
}
</style>