<template>
  <div class="wrap">
    <img src="@/assets/img/home/<USER>/bottom.png" class="img" />
    <!-- 创造点击劫持 -->
    <div class="bottomLi" v-for="(item, i) in list" :key="i" @click="handleClick(item)"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        { name: '网络安全', url: '' },
        { name: '数据安全', url: '' },
        { name: '资产安全', url: '' },
        { name: '运行态势', url: '' },
      ],
    }
  },
  methods: {
    handleClick(item) {
      console.log(item.name)
    },
  },
}
</script>

<style lang="less" scoped>
.wrap {
  position: relative;
  .img {
    width: 442px;
    height: 214px;
    display: block;
  }

  .bottomLi {
    width: 80px;
    height: 80px;
    box-sizing: border-box;
    cursor: pointer;
    // background-color: #fff;
    &:nth-child(2) {
      position: absolute;
      left: 36px;
      bottom: 20px;
    }
    &:nth-child(3) {
      position: absolute;
      left: 130px;
      bottom: 100px;
    }
    &:nth-child(4) {
      position: absolute;
      right: 130px;
      bottom: 100px;
    }
    &:nth-child(5) {
      position: absolute;
      right: 36px;
      bottom: 20px;
    }
  }
}
</style>