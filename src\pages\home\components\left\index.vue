<template>
  <div class="left-container">
    <FirstTitle :title="'资产概况'"></FirstTitle>
    <zcgk style="margin-bottom: 14px" :dataInfo="dataInfo"></zcgk>
    <FirstTitle :title="'云产品水位'"></FirstTitle>
    <ycpsw></ycpsw>
  </div>
</template>

<script>
import FirstTitle from '@/components/FirstTitle/index.vue'
import zcgk from '@/pages/home/<USER>/left/components/zcgk.vue'
import ycpsw from '@/pages/home/<USER>/left/components/ycpsw.vue'
import { getJbxx } from '@/api/home'
export default {
  components: { FirstTitle, zcgk, ycpsw },

  data() {
    return {
      dataInfo: {},
      dateValue: [], // 存储当前选择的日期范围
    }
  },
  mounted() {
    this.init()
    // 监听日期变化事件
    this.$bus.$on('dateChange', this.handleDateChange)
  },
  beforeDestroy() {
    // 移除事件监听，避免内存泄漏
    this.$bus.$off('dateChange', this.handleDateChange)
  },
  methods: {
    init() {
      this.getJbxx()
    },
    async getJbxx() {
      const res = await getJbxx()
      this.dataInfo = res.data.data
    },
    // 处理日期变化事件
    handleDateChange(dateValue) {
      console.log('left组件接收到日期变化:', dateValue)
      this.dateValue = dateValue
    },
  },
}
</script>

<style lang="less" scoped>
.left-container {
  width: 500px;
  height: 950px;
  background-color: #0b13204d;
}
</style>