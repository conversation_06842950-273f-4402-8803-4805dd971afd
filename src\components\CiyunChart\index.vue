<template>
  <div>
    <div :style="{width: width + 'px',height: height + 'px'}" :id="'wordsCharts' + id"></div>
  </div>
</template>

<script>
export default {
  name: "<PERSON><PERSON><PERSON><PERSON>",
  data() {
    return {}
  },
  props:{
    id:{
      type:String,
      default:"",
      require:true
    },
    //[{name:"",value:0}]
    chartsData:{
      type:Array,
      default:() => [],
      require: true
    },
    width:{
      type:String,
      default:"586"
    },
    height:{
      type:String,
      default:"500"
    },
    color:{
      type:Array,
      default:() => ['#FF741D','#00E487','#34BFFF','#A9DB52','#FFDC00']
    },
    //词云分布密集度
    gridSize: {
      type:Number,
      default:24
    }
  },
  computed: {},
  mounted() {
    this.initWordsCharts()
  },
  methods: {
    initWordsCharts() {
      const that = this
      let list = this.chartsData
      let myChart = this.$echarts.init(document.getElementById("wordsCharts" + this.id));
      let option = {
        tooltip: {
          show:false,
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '18',
          },
        },
        series: [
          {
            type: 'wordCloud',
            // The shape of the "cloud" to draw. Can be any polar equation represented as a
            // callback function, or a keyword present. Available presents are circle (default),
            // cardioid (apple or heart shape curve, the most known polar equation), diamond (
            // alias of square), triangle-forward, triangle, (alias of triangle-upright, pentagon, and star.

            shape: 'pentagon',

            // A silhouette image which the white area will be excluded from drawing texts.
            // The shape option will continue to apply as the shape of the cloud to grow.

            // Folllowing left/top/width/height/right/bottom are used for positioning the word cloud
            // Default to be put in the center and has 75% x 80% size.

            left: 'center',
            top: 'center',
            width: '100%',
            height: '100%',
            right: null,
            bottom: null,

            // Text size range which the value in data will be mapped to.
            // Default to have minimum 12px and maximum 60px size.

            sizeRange: [14, 18],

            // Text rotation range and step in degree. Text will be rotated randomly in range [-90, 90] by rotationStep 45

            rotationRange: [0, 0],
            rotationStep: 0,

            // size of the grid in pixels for marking the availability of the canvas
            // the larger the grid size, the bigger the gap between words.

            gridSize: that.gridSize,

            // set to true to allow word being draw partly outside of the canvas.
            // Allow word bigger than the size of the canvas to be drawn
            drawOutOfBound: false,

            // If perform layout animation.
            // NOTE disable it will lead to UI blocking when there is lots of words.
            layoutAnimation: true,

            // Global text style
            textStyle: {
              fontFamily: 'PingFangSC-Semibold',
              fontWeight: 600,
              color: function (params) {
                let colors = that.color
                return colors[parseInt(Math.random() * 5)];
                // return colors[0]
              },
            },
            emphasis: {
              focus: 'none',
            },

            // Data is an array. Each array item must have name and value property.
            data: list,
          },
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped>

</style>