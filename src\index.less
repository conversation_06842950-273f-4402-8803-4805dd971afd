html {
  font-size: 14px;
  height: 100%;
}

body {
  margin: 0;
  height: 100%;
  font-family: 'Microsoft YaHei' !important;
  // filter: grayscale(100%);
}
//滚动条样式
::-webkit-scrollbar {
  width: 0;
}
//小滚动条
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.min_scroll::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #0c1431;
}

/*定义滚动条轨道 内阴影+圆角*/
.min_scroll::-webkit-scrollbar-track {
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
  border-radius: 4px;
  background-color: #0c1431;
}

/*定义滑块 内阴影+圆角*/
.min_scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

.min_scroll::-webkit-scrollbar-thumb:hover {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

@pageWidth: 1920px;
@pageHeight: 1080px;

@font-face {
  font-family: 'pangmen';
  src: url('@/assets/font/PangMenZhengDaoBiaoTiTi-1.ttf');
}
@font-face {
  font-family: 'DIN-Bold';
  src: url('@/assets/font/DIN-Bold.otf');
}
@font-face {
  font-family: 'DIN-Black';
  src: url('@/assets/font/DIN-Black.otf');
}
@font-face {
  font-family: 'DIN-Medium';
  src: url('@/assets/font/DIN-Medium.otf');
}
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/font/优设标题黑.ttf');
}
@font-face {
  font-family: 'DS-DIGIT';
  src: url('@/assets/font/DS-DIGIT-4.ttf');
}

//黄白渐变
.yellow_linear {
  background: linear-gradient(0deg, #fff120 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//黄白渐变1
.yellow_linear1 {
  background: linear-gradient(0deg, #FFC460 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//黄色渐变
.yellow_only_linear {
  background: linear-gradient(0deg, #C3C400 0%, #E7E81F 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//蓝白渐变
.blue_linear {
  background: linear-gradient(0deg, #52c2f7 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//蓝白渐变2
.blue_linear2 {
  background: linear-gradient(0deg, #a0e3ff 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//蓝色渐变
.blue_only_linear {
  background: linear-gradient(0deg, #52c3f7 0%, #a8e0fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//蓝色渐变2
.blue_only_linear2 {
  background: linear-gradient(0deg, #a0e3ff 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//蓝色渐变3
.blue_only_linear3 {
  background: linear-gradient(0deg, #2F76FF 0%, #2F76FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//绿色渐变
.green_linear {
  background: linear-gradient(0deg, #51f67d 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//绿色渐变2
.green_linear2 {
  background: linear-gradient(0deg, #12ff9e 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//绿色渐变3
.green_linear3 {
  background: linear-gradient(0deg, #6ae4b2 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//红色渐变
.red_linear {
  background: linear-gradient(0deg, #ff4f34 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//红色渐变2
.red_linear2 {
  background: linear-gradient(90deg, #D9001B 0%, #FF4A61 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//橙色渐变
.orign_linear {
  background: linear-gradient(0deg, #ff7434 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//橙色渐变2
.orign_linear2 {
  background: linear-gradient(0deg, #BF7413 0%, #FFBA60 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//省略号
.cut_text {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  white-space: nowrap;
  outline: none;
}
// //自定义弹框样式
// .custom-class-dialog {
//   margin-top: 240px !important;
// }
//可点击
.cursor {
  cursor: pointer;
}
//tooltips宽度限制
.el-tooltip__popper {
  max-width: 400px !important;
}
// 饼底图旋转
@-webkit-keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

.rotate360 {
  -webkit-transform: rotate(360deg);
  animation: rotation 8s linear infinite;
  -moz-animation: rotation 8s linear infinite;
  -webkit-animation: rotation 8s linear infinite;
  -o-animation: rotation 8s linear infinite;
  border-radius: 100%;
  background-image: url('@/assets/img/common/pie_bkg.png');
  background-size: cover;
}

.rotate360_type2 {
  -webkit-transform: rotate(360deg);
  animation: rotation 8s linear infinite;
  -moz-animation: rotation 8s linear infinite;
  -webkit-animation: rotation 8s linear infinite;
  -o-animation: rotation 8s linear infinite;
  border-radius: 100%;
  background-image: url('@/assets/img/common/bingtudi.png');
  background-size: cover;
}

//图表pop
.charts_pop {
  background: rgba(0, 15, 55, 0.92);
  box-shadow: inset 0px 0px 30px 0px rgba(14, 156, 255, 0.7);
  border: 1px solid #0b64c3;
  &.el-popper[x-placement^='right'] .popper__arrow {
    border-right-color: rgba(14, 156, 255, 0.7);
  }
  &.el-popper[x-placement^='right'] .popper__arrow::after {
    border-right-color: rgba(0, 15, 55, 0.3);
  }
}

// 透明度淡入淡出
@-webkit-keyframes scaleout {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 20%;
  }

  100% {
    opacity: 1;
  }
}

.easy_out_in {
  animation: scaleout 2s infinite ease-in-out;
  -webkit-animation: scaleout 2s infinite ease-in-out;
}

// 边框流光
.streamer {
  position: relative;
  display: inline-block;
  // border-bottom: 2px solid #0b3866;

  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    top: 0px;
    left: 0;
    width: 45px;
    height: 10px;
    background-image: url('@/assets/img/common/streamer.png');
    background-size: cover;
    animation: animate1 2s linear infinite;
  }

  @keyframes animate1 {
    0% {
      left: -20px;
    }

    // 50%,
    100% {
      left: calc(100% - 45px);
    }
  }
}

//自定义颜色下拉级联框
.black_cascader {
  background: rgba(1, 31, 83, 1);
  border: 1px solid #707070;
  .el-cascader-node__label {
    color: #fff;
  }
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    background: #011f53;
  }
}

.flex-c{
  display: flex;
  align-items: center;
}
.flex-b{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-e{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-c-c{
  display: flex;
  align-items: center;
  justify-content: center;
}