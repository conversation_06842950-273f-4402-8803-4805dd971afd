<template>
  <div>
    <div class="form_box">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item prop="yyId">
          <el-select v-model="queryParams.yyId" placeholder="所属应用" clearable style="width: 160px">
            <el-option v-for="(item, i) in appOptions" :key="i" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item prop="deptId">
          <treeselect
            style="width: 160px; height: 34px"
            v-model="queryParams.deptId"
            :options="enabledDeptOptions"
            :show-count="true"
            placeholder="请选择归属部门"
          />
        </el-form-item>
        <el-form-item prop="level">
          <el-select v-model="queryParams.level" placeholder="告警等级" clearable>
            <el-option v-for="dict in levelOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="dateArr">
          <el-date-picker
            v-model="queryParams.dateArr"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <CommonTable :height="'500px'" :tableData="tableData"></CommonTable>
    </div>
    <CommonPagination :total="total" @pagination="changePage"></CommonPagination>
  </div>
</template>

<script>
import CommonTable from '@/components/CommonTable/index'
import CommonPagination from '@/components/CommonPagination/index'
import { listAllYy, deptTreeSelect } from '@/api/common'

export default {
  components: { CommonTable, CommonPagination },
  props: {},
  watch: {},
  data() {
    return {
      params: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 100,
      queryParams: {
        yyId: undefined,
        deptId: undefined,
        level: undefined,
        dateArr: [],
      },
      appOptions: [],
      enabledDeptOptions: [],
      levelOptions: [
        { label: '特别紧急', value: '1' },
        { label: '紧急', value: '2' },
        { label: '重要', value: '3' },
        { label: '一般', value: '4' },
      ],
      tableData: {
        thead: [
          { label: '县市区', property: 'area', width: 100, align: 'left' },
          { label: '得分', property: 'score', width: 120, align: 'left' },
          { label: '排名', property: 'ranking', width: 80, align: 'left' },
        ],
        tbody: [],
      },
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      listAllYy().then((res) => {
        this.appOptions = res.data.data
      })
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(JSON.parse(JSON.stringify(response.data.data)))
      })
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children)
        }
        return true
      })
    },
    changePage(obj) {
      this.params.pageNum = obj.page
      this.getList()
    },
  },
}
</script>

<style lang="less" scoped>
.table {
  padding: 0 20px;
}
.footer {
  margin: 20px;
}
.form_box{
  margin: 16px 0 0 20px;
}
/deep/.el-select {
  width: 120px;

  .el-input__inner {
    background: rgba(5, 89, 172, 0.3);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #7fb5ef;
    font-size: 16px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    height: 34px;
    line-height: 34px;
  }
}
/deep/.el-range-editor {
  background: rgba(5, 89, 172, 0.3);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #7fb5ef;

  .el-range-input {
    background: transparent;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    height: 34px;
    line-height: 34px;
  }
  .el-range-separator {
    color: #ffffff;
  }
}
/deep/.el-range-editor--small.el-input__inner{
  height: 34px;
}

/deep/.vue-treeselect {
  .vue-treeselect__control {
    background: rgba(5, 89, 172, 0.3);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #7fb5ef;
    font-size: 16px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    height: 34px;
    line-height: 34px;
  }

  .vue-treeselect__placeholder,
  .vue-treeselect__single-value {
    color: #ffffff;
    line-height: 34px;
  }

  .vue-treeselect__input {
    color: #ffffff;
  }
}
</style>