<!--
 * @Description: 公共弹框
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-05-27 09:17:52
 * @LastEditors: wjb
 * @LastEditTime: 2023-01-03 09:13:02
-->
<template>
  <el-dialog
    title=""
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal='modalFlag'
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="dialogFlag"
    :width="dialogWidth"
    :modal-append-to-body="false"
  >
    <div class="title-container">
      <div>
        <span>{{ title }}</span>
        <div v-if="selectFlag" class="select_content">
          <el-select v-model="value" placeholder="请选择" @change="chooseCounty()">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="tabbox" v-if="tabArrFlag">
          <div
            v-for="(item, i) in tabArr"
            :key="i"
            :class="{ divClick: tabIndex == i }"
            @click="clickTab(i, item.value)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <img class="close-btn" src="@/assets/img/common/close.png" @click="closeLayer" />
    </div>
    <div class="dialogContent">
      <slot />
    </div>
    <div class="footer" v-if="pageFlag">
      <el-pagination
        layout="prev, pager, next"
        :current-page.sync="params.pageNo"
        :total="params.total"
        :page-size="10"
        @current-change="changePage"
      ></el-pagination>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
export default {
  name: 'CommonDialog',
  props: {
    //标题
    title: {
      type: String,
      default: '',
    },
    //显示select
    selectFlag: {
      type: Boolean,
      default: false,
    },
    //显示flag
    dialogFlag: {
      type: Boolean,
      default: false,
    },
    //分页显示flag
    pageFlag: {
      type: Boolean,
      default: false,
    },
    //弹框宽度
    dialogWidth: {
      type: String,
      default: '960px',
    },

    // 下拉框数组
    options: {
      type: Array,
      default: () => [],
    },
    // 下拉框默认显示的index
    pValue: {
      type: String,
      default: '',
    },
    // tab切换显隐
    tabArrFlag: {
      type: Boolean,
      default: false,
    },
    // tab数组
    tabArr: {
      type: Array,
      default: () => [],
    },
    //是否限制遮罩
    modalFlag: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      params: {
        pageNo: 1,
        total: 10,
      },
      tabIndex: 0,
      value: 0,
      winTop: '40px',
    }
  },
  watch: {
    pValue: function (newVal, oldVal) {
      this.value = this.pValue
    },
    dialogFlag: function (newVal, oldVal) {
      if (newVal && this.tabArrFlag && this.selectFlag) {
        this.value = 0
        this.tabIndex = 0
        this.$emit('tab-active', 1)
        this.$emit('change-county', this.options[this.value].label)
      }
      if (newVal) {
        let that = this
        this.$nextTick(() => {
          setTimeout(() => {
            that.winTop =
              (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
              'px'
          }, 100)
        })
      }
    },
  },
  mounted() {},
  methods: {
    closeLayer() {
      this.$emit('close')
    },
    changePage(pageNo) {
      this.params.pageNo = pageNo
      this.$emit('change-page', this.params)
    },
    chooseCounty() {
      this.$emit('change-county', this.options[this.value].label)
    },
    clickTab(i, value) {
      this.$emit('tab-active', value)
      this.tabIndex = i
    },
  },
}
</script>
<style lang="less" scoped>
// 重置element-ui弹框
/deep/.el-dialog {
  background: rgba(0, 15, 55, 0.9);
  box-shadow: inset 0px 0px 30px 0px rgba(14, 156, 255, 0.7);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: 1px solid #0b64c3;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.title-container {
  display: flex;
  width: 100%;
  height: 52px;
  background: linear-gradient(270deg, rgba(36, 103, 193, 0.15) 0%, rgba(23, 90, 193, 0.69) 100%);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;

  > div:first-child {
    flex: 1;
    display: flex;
    align-items: center;
  }
}

.title-container span {
  margin-left: 20px;
  // height: 18px;
  line-height: 52px;
  margin-right: 12px;
  display: flex;
  align-items: center;

  &:nth-child(1) {
    font-size: 36px;
    font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
    // font-weight: bold;
    line-height: 50px;
    background: linear-gradient(0deg, #ffdc00 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    &::before {
      content: '';
      display: inline-block;
      background-image: url('@/assets/img/common/wrap_icon_left.png');
      background-size: cover;
      width: 24px;
      height: 24px;
      margin-right: 15px;
    }
  }

  &:nth-child(2) {
    margin-left: 10px;
    // font-size: 30px;
    font-family: 'YouSheBiaoTiHei';
    font-weight: 400;
    color: #f9fd26;
    line-height: 52px;
  }
}

.title-container .close-btn {
  float: right;
  margin: 15px;
  cursor: pointer;
}

/deep/.footer {
  height: 40px;
  line-height: 40px;
  text-align: right;
  padding-right: 20px;

  .btn-prev,
  .btn-next {
    background: transparent;
    color: #fff;
  }

  .number {
    background: transparent;
    color: #fff;
  }

  .active {
    color: #409eff;
  }
}

.dialogContent {
  overflow-x: hidden;
}

.select_content {
  width: 100px;
  height: 34px;
  background: rgba(5, 89, 172, 0.3);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #7fb5ef;

  /deep/.el-select {
    .el-input__inner {
      border: 0;
      background: transparent;
      font-size: 14px;
      font-family: Source Han Sans CN-Bold, Source Han Sans CN;
      // font-weight: bold;
      color: #ffffff;
      line-height: 34px;
      height: 34px;
    }

    .el-input .el-select__caret {
      color: #fff;
      line-height: 26px;
    }
  }
}

.tabbox {
  display: flex;

  > div {
    min-width: 52px;
    height: 34px;
    background: #4292e6;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    font-size: 16px;
    font-family: 'DIN-Black';
    font-weight: 400;
    color: #ffffff;
    line-height: 34px;
    text-align: center;
    margin-left: 7px;
    border: 1px solid transparent;
    cursor: pointer;
    padding:0 4px;
  }

  .divClick {
    background: rgba(5, 89, 172, 0.3);
    border: 1px solid #7fb5ef;
    color: #ffffff;
  }
}
</style>
