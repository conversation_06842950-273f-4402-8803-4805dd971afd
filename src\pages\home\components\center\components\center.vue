<template>
  <div class="wrap">
    <div class="bg">
      <div
        class="li flex-c"
        :class="i < 4 ? 'flex-left' : 'flex-right'"
        v-for="(item, i) in list"
        :key="i"
        :style="{
          backgroundImage: i < 4 ? 'url(' + leftBg + ')' : 'url(' + rightBg + ')',
        }"
        @click="checkDetail(item)"
      >
        <img :src="item.icon" class="icon-left" v-if="i < 4" />
        <div class="name_box">
          <div class="name">{{ item.name }}</div>
          <div class="num">{{ item.num }}{{ item.unit }}</div>
        </div>
        <img :src="item.icon" class="icon-right" v-if="i >= 4" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataInfo: {
      type: Object,
      default: () => {},
    },
    jbxxInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      list: [
        { name: '单位', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/dw.png') },
        { name: '应用', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/yy.png') },
        { name: '运维供应商总数', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/yzy.png') },
        { name: '人员', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/ry.png') },
        { name: '应用检测告警', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/gj.png') },
        { name: '安全隐患告警', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/yh.png') },
        { name: '云资源告警', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/aq.png') },
        { name: '数据库告警', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/gd.png') },
      ],
      leftBg: require('@/assets/img/home/<USER>/center_item_left.png'),
      rightBg: require('@/assets/img/home/<USER>/center_item_right.png'),
    }
  },
  watch: {
    dataInfo: {
      handler() {
        this.init()
      },
      immediate: true,
    },
    jbxxInfo: {
      handler() {
        this.init()
      },
      immediate: true,
    },
  },
  methods: {
    init() {
      this.list[0].num = this.jbxxInfo.dw
      this.list[1].num = this.jbxxInfo.yy
      this.list[2].num = this.jbxxInfo.gys
      this.list[3].num = this.jbxxInfo.ry
      this.list[4].num = this.dataInfo.jcTotal + this.dataInfo.ydTotal
      this.list[5].num = this.dataInfo.ahTotal
      this.list[6].num = this.dataInfo.dxTotal
      this.list[7].num = this.dataInfo.mcTotal
    },
    checkDetail(item) {
      this.$emit('checkDetail', item)
    },
  },
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  .bg {
    width: 412px;
    height: 489px;
    background: url('~@/assets/img/home/<USER>/center_bg.png') no-repeat;
    background-size: 100% 100%;
    .flex-left {
      justify-content: flex-start;
      .name_box {
        text-align: left;
      }
    }
    .flex-right {
      justify-content: flex-end;
      .name_box {
        text-align: right;
      }
    }
    .li {
      width: 230px;
      height: 70px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      box-sizing: border-box;
      position: absolute;
      cursor: pointer;
      display: flex;
      align-content: center;
      align-items: center;
      .icon-left {
        width: 28px;
        height: 28px;
        margin-left: 30px;
        margin-right: 22px;
        display: block;
      }
      .icon-right {
        width: 28px;
        height: 28px;
        margin-left: 20px;
        margin-right: 30px;
        display: block;
      }
      .name_box {
        .name {
          white-space: nowrap;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 20px;
          line-height: 20px;
          background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .num {
          white-space: nowrap;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 24px;
          line-height: 35px;
          background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      &:nth-child(1) {
        left: 50px;
        top: 40px;
      }
      &:nth-child(2) {
        left: 0px;
        top: 140px;
      }
      &:nth-child(3) {
        left: 0px;
        top: 240px;
      }
      &:nth-child(4) {
        left: 50px;
        top: 340px;
      }
      &:nth-child(5) {
        right: 50px;
        top: 40px;
        padding-left: 60px;
      }
      &:nth-child(6) {
        right: 0px;
        top: 140px;
        padding-left: 60px;
      }
      &:nth-child(7) {
        right: 0px;
        top: 240px;
        padding-left: 60px;
      }
      &:nth-child(8) {
        right: 50px;
        top: 340px;
        padding-left: 60px;
      }
    }
  }
}
</style>