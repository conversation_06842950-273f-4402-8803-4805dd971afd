<template>
  <div class="center-container">
    <top :dataInfo="dataInfo"></top>
    <center @checkDetail="checkDetail" :jbxxInfo="jbxxInfo" :dataInfo="dataInfo"></center>
    <bottom></bottom>

    <el-dialog :visible.sync="show" :modal="false" :show-close="false" class="dialog">
      <div class="header11 flex-b">
        <div class="flex-c">
          <div class="name">{{ listName }}列表</div>
          <div
            class="btn flex-c-c cursor"
            v-if="
              listName == '应用检测告警' ||
              listName == '安全隐患告警' ||
              listName == '云资源告警' ||
              listName == '数据库告警'
            "
            @click="goWarningList"
          >
            查看更多
          </div>
        </div>
        <img src="@/assets/img/home/<USER>/dialog_close.png" @click="show = false" class="closebtn cursor" />
      </div>
      <div class="tabList flex-c-c" v-if="listName=='应用检测告警'">
        <div
          class="tab flex-c-c"
          :class="tabIndex == i ? 'tab_active' : ''"
          v-for="(x, i) in tabList"
          :key="i"
          @click="changeTab(i)"
        >
          {{ x }}
        </div>
      </div>
      <dwDialog />
    </el-dialog>
  </div>
</template>

<script>
import top from '@/pages/home/<USER>/center/components/top.vue'
import center from '@/pages/home/<USER>/center/components/center.vue'
import bottom from '@/pages/home/<USER>/center/components/bottom.vue'
import dwDialog from '@/pages/home/<USER>/center/components/dwDialog.vue'
import { getGjxg, getJbxx } from '@/api/home'
export default {
  components: { top, center, bottom, dwDialog },
  data() {
    return {
      dataInfo: {},
      jbxxInfo: {},
      dateValue: [], // 存储当前选择的日期范围
      //弹框
      show: false,
      listName: '',
      tabList: [],
      tabIndex: 0,
      datalist: [],
      total: 1798,
      params: {
        pageNum: 1,
        pageSize: 10,
      },
    }
  },
  computed: {
    numList() {
      let arr = []
      let total = this.total
      while (total !== 0) {
        arr.push(total % 10)
        total = parseInt(total / 10)
      }
      return arr.reverse()
    },
  },
  mounted() {
    this.init()
    // 监听日期变化事件
    this.$bus.$on('dateChange', this.handleDateChange)
  },
  methods: {
    goWarningList() {
      let url = process.env.VUE_APP_ADMIN_URL + 'warning/warningList'
      window.open(url, '_blank')
    },
    init() {
      this.getJbxx()
    },
    // 处理日期变化事件
    handleDateChange(dateValue) {
      console.log('left组件接收到日期变化:', dateValue)
      this.dateValue = dateValue
      // 重新调用接口获取数据
      this.getGjxg()
    },
    async getJbxx() {
      const res = await getJbxx()
      this.jbxxInfo = res.data.data
    },
    async getGjxg() {
      // 构建请求参数
      const params = {}

      // 如果有选择日期范围，则添加到参数中
      if (this.dateValue && this.dateValue.length === 2) {
        params.startTime = this.dateValue[0]
        params.endTime = this.dateValue[1]
      } else {
        params.startTime = null
        params.endTime = null
      }

      const res = await getGjxg(params)
      this.dataInfo = res.data.data
    },
    changeTab(i) {
      this.tabIndex = i
    },
    checkDetail(value) {
      this.show = true
      this.listName = value.name
      if(this.listName=='应用检测告警'){
        this.tabList=['应用运行告警','基础设施告警']
      }
    },
    getList() {},
  },
}
</script>

<style lang="less" scoped>
.center-container {
  width: 840px;
  height: 986px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.dialog {
  .header11 {
    background: url('~@/assets/img/home/<USER>/dialog_titlebg.png');
    background-size: 100% 100%;
    width: 1070px;
    height: 70px;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 30px;
      color: #ffffff;
      line-height: 26px;
      text-align: left;
      padding: 0 30px;
      box-sizing: border-box;
    }
    .btn {
      padding: 10px 18px;
      box-sizing: border-box;
      background: linear-gradient(360deg, #003ca6 0%, #387af0 100%);
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #3998f0;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 19px;
      color: #ffffff;
      line-height: 19px;
    }
    .closebtn {
      width: 25px;
      height: 25px;
      margin: 30px;
    }
  }
  .tabList {
    margin: 20px 20px 0;
    .tab {
      width: 140px;
      height: 40px;
      background: #05224d;
      border: 1px solid #1c314d;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 18px;
      color: #acc3de;
      line-height: 16px;
      text-align: left;
      -webkit-text-stroke: 1px rgba(0, 0, 0, 0);
      cursor: pointer;
    }
    .tab_active {
      width: 140px;
      height: 40px;
      background: #074db2;
      border: 1px solid #5a85b0;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #ffffff;
      line-height: 16px;
      text-align: left;
    }
  }
}

/* 自定义el-dialog对话框添加背景图片 */
::v-deep .el-dialog {
  background: transparent;
  background-image: url('~@/assets/img/home/<USER>/dialog_bg.png') !important;
  background-size: 100% 100%;
  width: 1070px;
  padding-bottom: 24px;
  // height: 627px;
  /* 设置el-dialog__header、el-dialog__body、el-dialog__footer背景为透明 */
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    background-color: rgba(255, 255, 255, 0);
    padding: 0 !important;
  }
}

//翻页
.pagination {
  ::v-deep .el-pagination__total {
    color: #eaeced;
  }
  ::v-deep .el-input__inner {
    border-color: #78828f;
    background: transparent;
    color: #eaeced;
  }

  ::v-deep .btn-prev,
  ::v-deep .btn-next {
    color: #fff;
    background: #67859d;
    border-radius: 2px;
    margin: 0 6px;
    min-width: 26px !important;
    height: 30px;
    padding-left: 6px;
    padding-right: 6px;
  }
  ::v-deep .el-pager {
    li.active {
      font-weight: 600;
      background: #0166a6 !important;
    }
    li {
      font-weight: 400;
      min-width: 26px;
      height: 30px;
      background: #67859d !important;
      margin: 0 4px;
      border-radius: 2px;
      color: #fff;
    }
  }
  ::v-deep .el-pagination__jump {
    color: white;
  }
}
</style>