<template>
  <div class="topBarChart" :style="{ width: width + 'px' }">
    <div v-for="(item, index) in data" :key="index">
      <div class="title">
        <div class="title_left">
          <div class="num blue_linear">TOP{{ index + 1 }}</div>
          <div class="name blue_linear">{{ item.name }}</div>
        </div>
        <div class="title_right blue_linear">{{ item.count }} {{ item.unit }}</div>
      </div>
      <div class="bar_box">
        <div class="bkg"></div>
        <div class="bar" :style="{ width: item.count + '%', background: color[index] }"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'topBarChart',
  props: {
    width: {
      type: Number,
      default: 240,
    },
    data: {
      type: Array,
      default: [],
    },
    color: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {},
}
</script>

<style lang="less" scoped>
.topBarChart {
  .title {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    .title_left {
      display: flex;
      align-content: center;
      align-items: center;
      .num {
        font-size: 16px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
      }
      .name {
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        margin-left: 12px;
      }
    }
    .title_right {
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
    }
  }
  .bar_box {
    position: relative;
     margin-bottom: 4px;
    .bkg {
      width: 100%;
      height: 4px;
      background: #00526c;
      border-radius: 1px 1px 1px 1px;
    }
    .bar {
      position: absolute;
      left: 0;
      top: 0;
      height: 4px;
    }
  }
}
</style>
