import CryptoJS from 'crypto-js'

const KEY = CryptoJS.enc.Utf8.parse('0123456789szjh230123456789szjh23') // 秘钥

const iv = CryptoJS.enc.Utf8.parse('abcdef0123456789')

// AES对称加密
export function encryptAES(message, key = KEY) {
  const encrypted = CryptoJS.AES.encrypt(message, key, { iv: iv })
  return encrypted.toString()
}

// AES对称解密
export function decryptAES(encryptedData, key = KEY) {
  const decrypted = CryptoJS.AES.decrypt(encryptedData, key, { iv: iv })
  return decrypted.toString(CryptoJS.enc.Utf8)
}
