/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
import { isNumber } from 'lodash';
export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result
  
    const later = function() {
      // 据上一次触发时间间隔
      const last = +new Date() - timestamp
  
      // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
      if (last < wait && last > 0) {
        timeout = setTimeout(later, wait - last)
      } else {
        timeout = null
        // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
        if (!immediate) {
          result = func.apply(context, args)
          if (!timeout) context = args = null
        }
      }
    }
  
    return function(...args) {
      context = this
      timestamp = +new Date()
      const callNow = immediate && !timeout
      // 如果延时不存在，重新设定延时
      if (!timeout) timeout = setTimeout(later, wait)
      if (callNow) {
        result = func.apply(context, args)
        context = args = null
      }
  
      return result
    }
  }

export const numberFilter = (num, defaultNum) => {
  const defaultValue = defaultNum || '-';
  return isNumber(num) ? num : defaultValue;
}

/**
     * 分析每一列，找出相同的
     * @param data
     */
 export const getSpanArr=(arr)=> {
  for (let i = 0; i < arr.length; i++) {
    let row = i;
    // let col = i % this.colCount;
    if (row === 0) {
      // i 表示行 j表示列
      for (let j = 0; j < this.colFields.length; j++) {
        this.spanArr[i * this.colFields.length + j] = {
          rowspan: 1,
          colspan: 1,
        };
      }
    } else {
      for (let j = 0; j < this.colFields.length; j++) {
        // 当前和上一次的一样
        // 1. 合并所有列的相同数据单元格
        if (
          this.tableData[row][this.colFields[j]] ===
          this.tableData[row - 1][this.colFields[j]]
        ) {
          let beforeItem =
            this.spanArr[(row - 1) * this.colFields.length + j];
          this.spanArr[row * this.colFields.length + j] = {
            rowspan: 1 + beforeItem.rowspan, // 合并几行
            colspan: 1, // 合并几列,我这里只是跨行合并,不跨列合并,所以用的1
          };
          beforeItem.rowspan = 0;
          beforeItem.colspan = 0;
        } else {
          // rowspan 和 colspan 都为1表格此单元格不合并
          this.spanArr[row * this.colFields.length + j] = {
            rowspan: 1,
            colspan: 1,
          };
        }
      }
    }
  }
  // 对数据进行倒序
  let stack = [];

  for (let i = 0; i < this.colFields.length; i++) {
    for (let j = 0; j < this.tableData.length; j++) {
      // console.log("i=" + i + " j=" + j);
      // i 表示列 j表示行
      if (j === 0) {
        if (this.spanArr[j * this.colFields.length + i].rowspan === 0) {
          stack.push(this.spanArr[j * this.colFields.length + i]);
        }
      } else {
        if (this.spanArr[j * this.colFields.length + i].rowspan === 0) {
          stack.push(this.spanArr[j * this.colFields.length + i]);
        } else {
          stack.push(this.spanArr[j * this.colFields.length + i]);
          while (stack.length > 0) {
            let pop = stack.pop();
            let len = stack.length;
            this.spanArr[(j - len) * this.colFields.length + i] = pop;
          }
        }
      }
    }
  }
  // console.log(this.spanArr);
}