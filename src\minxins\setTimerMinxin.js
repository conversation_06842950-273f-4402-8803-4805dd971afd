import {
    isArray
} from 'lodash';
export default {
    data(){
        return{
            minxinTimer: null,
            fn: null,
            timerIntervalCount: 30000,
            
        }
    },
    methods: {
        getData(fn) {
            (fn || []).forEach(f => {
                f();
            })
        },
        setMinxinsTimer(fn) {
            if (isArray(fn)) {
                this.minxinTimer = setInterval(() => this.getData(fn), this.timerIntervalCount);
            } else {
                this.minxinTimer = setInterval(fn, this.timerIntervalCount);
            }
            
        }
    },
    destroyed() {
        clearInterval(this.minxinTimer);
        this.minxinTimer = null;
    }

}