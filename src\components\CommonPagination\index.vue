<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :pager-count.sync='pagerCount'
      prev-text="上一页"
      next-text="下一页"
      :total="total"
      v-bind="$attrs"
      :page-sizes="pageSizes"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'CommonPagination',
  props: {
    total: {
      required: true,
      type: Number,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 10,
    },
    pagerCount: {
      type: Number,
      default: 5,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 15, 20, 30, 40, 50]
      },
    },
    layout: {
      type: String,
      default: 'prev, pager, next',
    },
    background: {
      type: <PERSON><PERSON><PERSON>,
      default: true,
    },
    autoScroll: {
      type: <PERSON>olean,
      default: true,
    },
    hidden: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      },
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      },
    },
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', {
        page: this.currentPage,
        limit: val,
      })
    },
    handleCurrentChange(val) {
      this.$emit('pagination', {
        page: val,
        limit: this.pageSize,
      })
    },
  },
}
</script>

<style lang="less">
.pagination-container {
  padding: 16px 8px;
  text-align: right;
  .el-pagination .el-pagination__sizes .el-input .el-input__inner {
    height: 28px;
  }
  .el-pagination .el-pager .number {
    width: 30px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: transparent;
    color: #ffffff;
    border-radius: 5px 5px 5px 5px;
    opacity: 1;
    font-size: 16px;
    font-weight: 400;
    margin: 0 4px;
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #ffffff;
    color: #010d30;
  }
  .el-pagination .el-pager .number.active {
    background-color: #ffffff;
    color: #010d30;
  }
  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    background: transparent;
  }
  .el-pagination .btn-prev span,
  .el-pagination .btn-next span {
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
}

.pagination-container.hidden {
  display: none;
}
.el-pagination span:not([class*='suffix']) {
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  float: left;
}
</style>
