<template>
  <div class="wrapBox">
    <img class="corner corner1" src="@/assets/img/common/wrap_corner_topLeft.png" alt="" />
    <img class="corner corner2" src="@/assets/img/common/wrap_corner_topRight.png" alt="" />
    <img class="corner corner3" src="@/assets/img/common/wrap_corner_bottomLeft.png" alt="" />
    <img class="corner corner4" src="@/assets/img/common/wrap_corner_bottomRight.png" alt="" />
    <div class="top_title" v-if="title">
      <div class="title_left">
        <img src="@/assets/img/common/wrap_icon_left.png" alt="" />
        <div class="title yellow_linear" @click="titleClick" v-if="!title2">{{ title }}</div>
        <div class="title_box" v-if="title2">
          <div class="title yellow_linear" :class="titleIndex == 0 ? 'on' : ''" @click="title2Click(0, title)">
            {{ title }}
          </div>
          <div class="line"></div>
          <div class="title yellow_linear" :class="titleIndex == 1 ? 'on' : ''" @click="title2Click(1, title2)">
            {{ title2 }}
          </div>
          <div class="line" v-if="title3"></div>
          <div
            class="title yellow_linear"
            v-if="title3"
            :class="titleIndex == 2 ? 'on' : ''"
            @click="title2Click(2, title3)"
          >
            {{ title3 }}
          </div>
          <div class="line" v-if="title4"></div>
          <div
            class="title yellow_linear"
            v-if="title4"
            :class="titleIndex == 3 ? 'on' : ''"
            @click="title2Click(3, title4)"
          >
            {{ title4 }}
          </div>
        </div>
        <div v-if="leftClickText" class="left_click" @click="leftTextClick">{{ leftClickText }}</div>
      </div>
      <div v-if="showRight&&!type" class="title_right">
        <img v-if="number == 0" src="@/assets/img/common/wrap_icon_right.png" alt="" />
        <div v-else class="title_right_number">总数: {{ number }}人</div>
      </div>
      <div v-else class="title_right">
        <el-select
          :style="{ width: selectWidth }"
          v-model="value"
          placeholder="请选择"
          @change="changeSelect"
          v-if="type == 1"
        >
          <el-option v-for="item in selectList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <div class="right_btn yellow_linear" v-if="type == 2" @click="rightTextClick(title)">详情</div>
        <div class="right_btn1" v-if="type == 22" @click="rightTextClick(title)">详情</div>
        <div v-if="type == 3" class="right_tab">
          <div v-for="(item, i) in tabData" :key="i" :class="{ tab_active: i == tabIndex }" @click="tabActiveFn(i)">
            {{ item }}
          </div>
        </div>
        <el-date-picker
          v-if="type == 4"
          v-model="dateVal"
          type="date"
          placeholder="选择日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="changeDate"
        ></el-date-picker>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'wrapBox',
  props: {
    //右侧下拉框宽度
    selectWidth: {
      type: String,
      default: '100px',
    },
    title: {
      type: String,
      default: '',
    },
    //左侧标题2（可切换）
    title2: {
      type: String,
      default: '',
    },
    //左侧标题3（可切换）
    title3: {
      type: String,
      default: '',
    },
    //左侧标题4（可切换）
    title4: {
      type: String,
      default: '',
    },
    //是否显示右侧
    showRight: {
      type: Boolean,
      default: true,
    },
    leftClickText: {
      type: String,
      default: '',
    },
    //右侧内容类型 1：下拉框  2：详情按钮   3: 右侧tab筛选
    type: {
      type: Number,
      default: 0,
    },
    selectList: {
      type: Array,
      default: () => [],
    },
    selectValue: {
      type: Number,
      default: 0,
    },
    selectDateVal: {
      type: String,
      default: '',
    },
    number: {
      type: Number,
      default: 0,
    },
    // 右侧tab数组
    tabData: {
      type: Array,
      default: () => [],
    },
    // 右侧tab数组选中
    tabActiveIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      value: 0,
      titleIndex: 0, //左侧标题选中值
      tabIndex: 0, //右侧tab选中值
      dateVal: '', //日期
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.value = this.selectValue
      this.dateVal = this.selectDateVal
    })
  },
  watch: {
    selectValue: function (newVal, oldVal) {
      this.value = this.selectValue
      this.dateVal = this.selectDateVal
    },
  },
  beforeDestroy() {},
  methods: {
    changeSelect(val) {
      if (this.type == 1) {
        this.$emit('changeTrendType', val)
      }
    },
    titleClick() {
      this.$emit('titleClick', this.title)
      this.$emit('openZdryDialog', 'openZdryDialog')
    },
    //左侧标题切换
    title2Click(index, title) {
      this.titleIndex = index
      this.$emit('title2Click', title)
    },
    leftTextClick() {
      this.$emit('leftTextClick', this.leftClickText)
    },
    //右侧标题点击-弹框
    rightTextClick(txt) {
      //按照标题显示弹框
      this.$emit('rightTextClick', txt)
    },
    // 右侧tab切换
    tabActiveFn(i) {
      this.tabIndex = i
      this.$emit('tabIndex', i)
    },
    //日期切换
    changeDate(val) {
      this.$emit('changeDate', val)
    },
  },
}
</script>

<style lang="less" scoped>
.wrapBox {
  position: relative;
  background: rgba(0, 15, 55, 0.2);
  box-shadow: inset 0px 0px 30px 0px rgba(14, 156, 255, 0.7);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: 1px solid #0b64c3;
  box-sizing: border-box;
  .corner {
    width: 13px;
    height: 13px;
  }
  .corner1 {
    position: absolute;
    left: 0;
    top: 0;
  }
  .corner2 {
    position: absolute;
    right: 0;
    top: 0;
  }
  .corner3 {
    position: absolute;
    left: 0;
    bottom: 0;
  }
  .corner4 {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .top_title {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px 0 15px;
    box-sizing: border-box;
    height: 52px;
    background: linear-gradient(270deg, rgba(23, 90, 193, 0.69) 0%, rgba(36, 103, 193, 0.15) 100%);
    .title_left {
      display: flex;
      align-content: center;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
        margin-right: 10px;
      }
      .title {
        font-size: 34px;
        font-family: YouSheBiaoTiHei;
        line-height: 50px;
        cursor: pointer;
      }
      .title_box {
        display: flex;
        align-items: center;
        align-content: center;

        .title {
          opacity: 0.5;
        }

        .title.on {
          opacity: 1;
        }

        .line {
          width: 2px;
          height: 27px;
          background: #d8d8d8;
          margin: 0 16px;
        }
      }
      .left_click {
        margin-left: 24px;
        cursor: pointer;
        width: 100px;
        height: 34px;
        background: linear-gradient(360deg, #003ca6 0%, #387af0 100%);
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #afdcfb;
        font-size: 20px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        color: #ffffff;
        text-align: center;
        line-height: 34px;
      }
    }
    .title_right {
      img {
        width: 83px;
        height: 10px;
      }
      .title_right_number {
        font-size: 24px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        font-weight: bold;
        line-height: 26px;
        background: linear-gradient(360deg, #52ffe9 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /deep/.el-select {
        width: 100px;

        .el-input__inner {
          background: rgba(5, 89, 172, 0.3);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #7fb5ef;
          font-size: 16px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          height: 34px;
          line-height: 34px;
        }
      }

      /deep/.el-date-editor {
        width: 140px;
        .el-input__inner {
          background: rgba(5, 89, 172, 0.3);
          color: #fff;
        }
      }

      .right_btn {
        font-size: 36px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        cursor: pointer;
      }
      .right_btn1 {
        font-size: 18px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        font-weight: 700;
        color: #91d1f4;
        line-height: 24px;
        cursor: pointer;
      }

      .right_tab {
        display: flex;
        align-items: center;

        div {
          padding: 0 10px;
          height: 34px;
          background: rgba(5, 89, 172, 0.3);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #7fb6f0;
          font-size: 16px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          line-height: 16px;
          -webkit-background-clip: text;
          line-height: 34px;
          margin-left: 8px;
          cursor: pointer;
          min-width: 72px;
          box-sizing: border-box;
          text-align: center;
        }

        .tab_active {
          background: #4292e7;
          border: 1px solid #4292e7;
          color: #fff;
        }
      }
    }
  }
}
</style>
