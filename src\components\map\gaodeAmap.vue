<template>
  <div class="map-container">
    <div class="map-container">
      <amap :zoom="zoom" :zooms="zooms" :center="center" :mapStyle="mapstyle" ref="map" id="map">
        <amap-marker
          v-if="spjkShow"
          v-for="(item, i) in points"
          :key="i"
          :position="item.xy"
          @click="getPointInfo(item)"
          :offset="[-22, -40]"
          :label="{
            content: item.name,
            direction: 'bottom',
          }"
          :icon="markerIcon"
          :zIndex="999"
        />
      </amap>
    </div>

    <img class="spjk" src="@/assets/img/common/spjk.png" alt="" @click="spjkClick()" />
    <img v-show="spjkShow" class="spjk-icon" src="@/assets/img/common/spjk-icon.png" alt="" />

    <div class="circleBox">
      <div class="avatar"></div>
      <div class="inner"></div>
      <div class="inner container"></div>
      <div class="inner outter"></div>
    </div>
    <div class="zlimg" @click="goDirective">
      <div class="zlimg1">
        <img class="imgMsg" src="@/assets/img/common/zhiling3.png" alt="" />
      </div>
    </div>
    <div class="radioDiv">
      <div class="radioitem" v-for="(item, i) in radioArr" :key="i + 'a'" @click="clickRadio(i)">
        <div :class="{ radioActive: radioIndex == i }">{{ radioIndex == i ? '√' : '' }}</div>
        <div>{{ item }}</div>
      </div>
    </div>
    <!--视频点位弹窗-->
    <CommonDialog
      :dialogFlag="videoDialogFlag"
      :title="'视频监控'"
      :dialogWidth="'540px'"
      @close="videoDialogFlag = false"
      :tabArrFlag="false"
    >
      <venueVideo
        ref="video"
        :visible="visible"
        :videoConfig="videoConfig"
        :code="code"
        :createflag="createflag"
        :setflag="setflag"
        :destoryflag="destoryflag"
      ></venueVideo>
    </CommonDialog>
  </div>
</template>

<script>
import DHWs from '../../components/Video/DHWs.js'
const DHWsInstance = DHWs.getInstance({
  reConnectCount: 2,
  connectionTimeout: 30 * 1000000,
  messageEvents: {
    loginState() {
      console.log('aaaa')
    },
  },
})
import VenueVideo from '../../components/Video/VenueVideo'
import CommonDialog from '@/components/Commondialog/index.vue'
import { getRegionStatistics } from '@/api/home'
import jinhuaJson from '@/areaJson/jinhua'
export default {
  name: 'gaodeAmap',
  components: {
    CommonDialog,
    VenueVideo,
  },
  data() {
    return {
      markerIcon: require('@/assets/img/home/<USER>'), //打点图标
      zoom: 9,
      zooms: [9, 12],
      center: [119.95709195628763, 29.12321470182451],
      mapstyle: 'amap://styles/darkblue',
      num_status: false,
      colorList: {
        1: 'rgba(104, 206, 118,0.7)',
        2: 'rgba(255, 211, 57,0.7)',
        3: 'rgba(240, 121, 49,0.7)',
      },
      mapList: [],
      jinhuajson: '',
      radioArr: ['异动人员', '重大事件', '执行评分'],
      radioIndex: 2,
      spjkShow: false,
      points: [
        { name: 'asdaf', xy: [119.7, 29.3], code: 'Gfio1xWiA1E083LKKVAFUV' },
        { name: 'asdaf2', xy: [119.9, 29.3], code: 'Gfio1xWiA1E083LKKVAFUV' },
        { name: 'asdaf3', xy: [119.9, 28.8], code: 'Gfio1xWiA1E083LKKVAFUV' },
      ], //点位

      //视频相关
      ws: DHWsInstance,
      videoDialogFlag: false,
      visible: false,
      videoConfig: {
        ctrlType: 'playerWin',
        ctrlCode: 'ctrl1',
        ctrlProperty: {
          displayMode: 1,
          splitNum: 1,
        },
        visible: true,
        domId: 'dom1',
      },
      code: '',
      createflag: false,
      setflag: false,
      destoryflag: false,
    }
  },
  mounted() {
    var deptId = localStorage.getItem('deptId')
    if (deptId) {
      if (deptId == 202) {
        this.jinhuajson = jinhuaJson[0]
      } else {
        if(deptId==221){
          deptId=220
        }
        this.jinhuajson = {
          type: 'FeatureCollection',
          features: [jinhuaJson[0].features.find((item) => item.properties.id == deptId)],
        }
        this.zoom=10
        this.center=jinhuaJson[0].features.find((item) => item.properties.id == deptId).center
      }
      window.labelClick = this.labelClick
      this.getRegionStatistics(2)
      setTimeout(() => {
        this.initHomeMap()
        if (deptId == 202) {
          this.addMapMask()
        }
      }, 300)
    } else {
      this.$bus.$on('deptId', (res) => {
        if (res == 202) {
          this.jinhuajson = jinhuaJson[0]
        } else {
          let resdeptId=''
          if(res==221){
            resdeptId=220
          }else{
            resdeptId=res
          }
          this.jinhuajson = {
            type: 'FeatureCollection',
            features: [jinhuaJson[0].features.find((item) => item.properties.id == resdeptId)],
          }
          this.zoom=10
          this.center=jinhuaJson[0].features.find((item) => item.properties.id == deptId).center
        }
      })
      window.labelClick = this.labelClick
      this.getRegionStatistics(2)
      setTimeout(() => {
        this.initHomeMap()
        if (deptId == 202) {
          this.addMapMask()
        }
      }, 300)
    }
  },
  methods: {
    //初始化地图
    initHomeMap() {
      const labelJson = umap2d.utils.createGeoJSON()
      if (this.$refs.map.$map) this.$refs.map.$map.setZoomAndCenter(this.zoom, this.center)
      // 地图加载
      let me = this
      if (!this.infoWindow)
        this.infoWindow = new AMap.InfoWindow({
          offset: new AMap.Pixel(0, -5),
        })
      this.infoWindow.close()
      if (window.regionlyr) window.regionlyr.remove()
      this.jinhuajson.features.map((res) => {
        res.properties.style = { fillColor: '#174d68' }
        let feat = umap2d.utils.createFeature('point')
        feat.geometry.coordinates = res.properties.centroid

        let fz
        let unit = ''
        let mapItem = this.mapList.find((a) => {
          return a.dutyPlace == res.properties.name
        })
        fz = mapItem&&mapItem.count ? mapItem.count : ''
        if (fz) {
          unit = this.getUnit()
        }
        let grade = this.computeGrade(fz)
        //*/设置地图区块颜色
        if (grade) {
          res.properties.style = {
            //fillColor: this.colorList[grade] //根据地图分数给定区划颜色
            fillColor: 'rgba(26,132,208,.7)',
          }
        }
        let content = ''
        let content1 = `
              <div onclick="labelClick('${res.properties.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #FF6D6D 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${fz} <span style="font-size: 14px;margin-left: -7px">${unit}</span> </div>
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${res.properties.name}</div>
                </div>
              </div>
               `
        let content2 = `
              <div onclick="labelClick('${res.properties.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #33BEFF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${fz} <span style="font-size: 14px;margin-left: -7px">${unit}</span> </div>
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${res.properties.name}</div>
                </div>
              </div>
               `
        let content3 = `
              <div onclick="labelClick('${res.properties.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #F5CC53 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${fz} <span style="font-size: 14px;margin-left: -7px">${unit}</span> </div>
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${res.properties.name}</div>
                </div>
              </div>
               `
        switch (mapItem.level) {
          case 1:
            content = content1
            break
          case 2:
            content = content2
            break
          case 3:
            content = content3
            break
        }
        //*/
        feat.properties = {
          name: res.properties.name,
          content: content,
        }
        labelJson.features.push(feat)
      })
      // debugger
      let regionlyr = new umap2d.PolygonLayer({
        layerId: 'regionlyr',
        data: this.jinhuajson,
        map: this.$refs.map.$map,
        showPop: false,
        labelField: 'label',
        showLabel: false,
        zooms: [5, 10],
        style: {
          label: {
            fontSize: 16,
            fillColor: '#FFFFFF',
            strokeWidth: 2,
            strokeColor: '#3D3D3D',
          },
          polygon: {
            fillOpacity: 1,
            fillColor: '#539FF7',
            strokeWeight: 2,
            strokeColor: '#52FFFD',
          },
        },
      })
      regionlyr.add()
      window.regionlyr = regionlyr
      me.loadLabels(labelJson)
    },
    //点击图层显示弹框
    labelClick(name) {
      this.$emit('showMapDetails', this.radioIndex, name)
    },
    //地图影像遮罩
    addMapMask() {
      let options = {
        subdistrict: 1,
        extensions: 'all',
        level: 'district',
      }
      let district = new AMap.DistrictSearch(options)
      district.search('金华市', (status, result) => {
        this.$refs.map.$map.setLayers([new AMap.TileLayer.Satellite()]) //设置为影像地图
        // 外多边形坐标数组和内多边形坐标数组
        let outer = [
          new AMap.LngLat(-360, 90, true),
          new AMap.LngLat(-360, -90, true),
          new AMap.LngLat(360, -90, true),
          new AMap.LngLat(360, 90, true),
        ]
        let holes = result.districtList[0].boundaries
        let pathArray = [outer]
        pathArray.push.apply(pathArray, holes)
        if (window.mask) {
          window.mask.setMap(this.$refs.map.$map)
          return
        }
        let polygon = new AMap.Polygon({
          pathL: pathArray,
          strokeColor: '#00eeff', //边框线颜色
          strokeWeight: 2,
          fillColor: '#000d22', //遮罩图层颜色
          fillOpacity: 0.85,
        })
        polygon.setPath(pathArray)
        this.$refs.map.$map.add(polygon)
        window.mask = polygon
      })
    },
    //根据地图分数计算好坏等级
    computeGrade(score) {
      if (score >= 85) {
        return 1
      } else if (score < 70) {
        return 3
      } else {
        return 2
      }
    },
    //加载区划label
    loadLabels(json) {
      if (window.labelLayer) window.labelLayer.remove()
      // debugger
      let size = []
      if (this.radioIndex == 0) {
        size = [90, 100]
      } else {
        size = [65, 100]
      }
      let layer = new umap2d.DivIconMarkerLayer({
        layerId: 'labelLayer',
        map: this.$refs.map.$map,
        data: json,
        showLabel: false,
        labelField: 'name',
        style: {
          size: size,
        },
        zooms: [5, 10],
      })
      layer.add()
      window.labelLayer = layer
    },
    //点位点击
    getPointInfo(item) {
      this.code = item.code
      this.videoDialogFlag = true
    },
    //跳转指令
    goDirective() {
      console.log('跳转后台指令')
      location.href = process.env.VUE_APP_ADMIN_URL + '/direct/direction'
    },
    //左下角选择框点击
    clickRadio(i) {
      this.radioIndex = i
      this.getRegionStatistics(i)
    },
    // 查询事件基本信息列表
    getRegionStatistics(type) {
      getRegionStatistics({ type: type }).then((res) => {
        this.mapList = res.data.data
      })
    },
    // 视频监控点击
    spjkClick() {
      this.spjkShow = !this.spjkShow
    },
    //设置地图label单位
    getUnit() {
      let unit = ''
      switch (this.radioIndex) {
        case 0:
          unit = '人'
          break
        case 1:
          unit = '件'
          break
        case 2:
          unit = '分'
          break
      }
      return unit
    },

    //----------视频相关------------
    //打开视频
    showVenueVideo() {
      this.newVenueVideo()
      this.visible = true
    },
    //关闭视频
    closeVideo() {
      this.visible = false
    },
    newVenueVideo() {
      this.logoutVideo()
      this.getMinitorData()
    },
    //退出视频
    logoutVideo() {
      this.allVideoDestory()
      this.logout()
    },
    //视频配置相关
    getMinitorData() {
      if (this.code !== '') {
        if (this.isLogin) {
          this.setAllvideo()
        } else {
          this.loginVideo()
        }
      } else {
        this.code = ''
        if (this.isLogin) {
          this.setAllvideo()
        }
      }
      // });
    },
    loginVideo() {
      // let DHWsInstance = this.ws;
      const _this = this
      this.ws.detectConnectQt().then((res) => {
        if (res) {
          // 连接客户端成功
          this.ws.login({
            loginIp: '************',
            loginPort: '8281',
            userName: 'syzx',
            userPwd: 'syzx1234',
            token: '',
          })
          this.ws.on('loginState', (res) => {
            if (res) {
              console.log('登录成功')
              _this.createAllvideo()
              _this.setAllvideo()
              this.isLogin = true
            } else {
              console.log('登录失败')
            }
          })
        } else {
          // 连接客户端失败
          console.log('连接客户端失败')
        }
      })
    },
    createAllvideo() {
      let that = this
      this.createflag = false
      setTimeout(() => {
        that.createflag = true
      }, 100)
    },
    setAllvideo() {
      let that = this
      this.setflag = false
      setTimeout(() => {
        that.setflag = true
      }, 100)
    },
    allVideoDestory() {
      let that = this
      this.destoryflag = false
      setTimeout(() => {
        that.destoryflag = true
      }, 100)
    },
    logout() {
      this.ws.logout({
        loginIp: '*************',
      })
      this.isLogin = false
    },
  },
  watch: {
    mapList() {
      setTimeout(() => {
        this.initHomeMap()
      }, 300)
    },
    videoDialogFlag(val) {
      if (val) {
        this.showVenueVideo()
      } else {
        this.closeVideo()
      }
    },
  },
}
</script>

<style scoped lang="less">
.map-container {
  width: 820px;
  height: 580px;
  position: relative;

  .circleBox {
    position: absolute;
    // width: 300px;
    // height: 100px;
    margin: 10px auto;
    right: 82px;
    bottom: 128px;

    div.inner {
      width: 45px;
      height: 45px;
      background: #247cd9;
      border-radius: 50%;
      position: fixed;
      // right: 0;
      // top: 0;
      // left: 50%;
      // margin-left: -45px;
      // margin-top: 152px;
      // z-index: 10;
      animation-duration: 2.4s;
      -webkit-animation-name: state1;
      animation-name: state1;
      -webkit-animation-timing-function: linear;
      animation-timing-function: linear;
      -webkit-animation-iteration-count: infinite;
      animation-iteration-count: infinite;
      opacity: 0;
    }

    @keyframes state1 {
      0% {
        opacity: 0.5;
        -webkit-transform: scale(1);
        transform: scale(1);
      }

      100% {
        opacity: 0;
        border: 1px solid rgb(5, 1, 56);
        -webkit-transform: scale(4.5);
        transform: scale(4.5);
      }
    }

    .avatar {
      // position: fixed;
      // left: 50%;
      // margin-left: -28px;
      // margin-top: 172px;
      border-radius: 50%;
      width: 56px;
      height: 56px;
      background-color: transparent;
      // z-index: 11;
    }

    .container {
      animation-delay: 0.8s;
    }

    .outter {
      animation-delay: 1.5s;
    }
  }

  .zlimg {
    background-image: url('@/assets/img/common/zhiling1.png');
    background-size: cover;
    width: 146px;
    height: 147px;
    position: absolute;
    right: 40px;
    bottom: 40px;
    cursor: pointer;
    // -webkit-transform: rotate(360deg);
    animation: rotation 3s linear infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    // -moz-animation: rotation 3s linear infinite;
    // -webkit-animation: rotation 3s linear infinite;
    // -o-animation: rotation 3s linear infinite;

    .zlimg1 {
      background-image: url('@/assets/img/common/zhiling2.png');
      background-size: cover;
      width: 113px;
      height: 113px;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: rotation1 1.5s linear infinite;
      border-radius: 100%;

      .imgMsg {
        animation: rotation2 3s linear infinite;
      }
    }
  }

  @keyframes rotation {
    from {
      -webkit-transform: rotate(0deg);
    }

    to {
      -webkit-transform: rotate(360deg);
    }
  }

  @keyframes rotation1 {
    from {
      -webkit-transform: rotate(0deg);
    }

    to {
      -webkit-transform: rotate(-360deg);
    }
  }

  @keyframes rotation2 {
    from {
      -webkit-transform: rotate(-360deg);
    }

    to {
      -webkit-transform: rotate(0deg);
    }
  }

  .spjk {
    width: 56px;
    height: 56px;
    position: absolute;
    right: 10px;
    top: 13px;
    cursor: pointer;
  }

  .spjk-icon {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 54px;
    cursor: pointer;
  }

  .radioDiv {
    position: absolute;
    width: 115px;
    background: linear-gradient(180deg, rgba(14, 26, 64, 0.8) 0%, rgba(6, 64, 105, 0.8) 100%);
    box-sizing: border-box;
    border: 1px solid #0b64c3;
    box-shadow: inset 0px 0px 10px 0px rgba(14, 156, 255, 0.7);
    left: 18px;
    bottom: 14px;
    padding-left: 14px;
    padding-top: 14px;
    box-sizing: border-box;

    .radioitem {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      cursor: pointer;

      > div:first-child {
        width: 14px;
        height: 14px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #ffffff;
      }

      > div:nth-child(2) {
        margin-left: 9px;
        font-size: 16px;
        font-family: 'DIN-Medium';
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
        -webkit-background-clip: text;
      }
    }

    .radioActive {
      // background: rgba(139, 110, 0, 0.2);
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 178.45999270677567, 0, 1)) 1 1 !important;
      background: linear-gradient(to top, #ffdc00, #fffcf3);
      -webkit-background-clip: text;
      color: transparent;
      text-align: center;
      line-height: 14px;
      font-weight: 700;
      cursor: pointer;
    }
  }
}

/*点位label样式*/
/deep/ .map-container {
  .amap-marker-label {
    background: #00b6f3;
  }
}
</style>
