<template>
  <div class="wrap">
    <div class="item">
      <div class="itemList flex-b">
        <div class="liBox flex-b" v-for="(item, i) in list1" :key="i">
          <div class="li" style="width: 115px">
            <div class="key">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
          <img v-if="i !== list1.length - 1" src="@/assets/img/home/<USER>/divider_horizontal.png" class="divider-v" />
        </div>
      </div>
      <img src="@/assets/img/home/<USER>/divider_horizontal.png" class="divider-h" />
      <div class="itemList flex-b">
        <div class="liBox flex-b" v-for="(item, i) in list2" :key="i">
          <div class="li" style="width: 140px">
            <div class="key">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
          <img v-if="i !== list1.length - 1" src="@/assets/img/home/<USER>/arrow_gold.png" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      list1: [
        { name: '特急工单', value: 0 },
        { name: '紧急工单', value: 0 },
        { name: '一般工单', value: 0 },
      ],
      list2: [
        { name: '发起', value: 0 },
        { name: '处置', value: 0 },
        { name: '完成', value: 0 },
      ],
    }
  },
  watch: {
    dataInfo: {
      handler() {
        this.init()
      },
      immediate: true,
    },
  },
  methods: {
    init() {
      this.list1[0].value = this.dataInfo.ggd
      this.list1[1].value = this.dataInfo.zgd
      this.list1[2].value = this.dataInfo.dgd
      this.list2[1].value = this.dataInfo.gdcz
      this.list2[2].value = this.dataInfo.gdwc
    },
  },
}
</script>

<style lang="less" scoped>
.wrap {
  padding: 0 12px;
  box-sizing: border-box;
}
.item {
  background: linear-gradient(to right, #026ef100, #026ef13b, #026ef100);
  margin: 10px 0 16px 0;
  padding: 12px;
  box-sizing: border-box;
  .itemList {
    padding-top: 12px;
    box-sizing: border-box;
    .liBox {
      // flex: 1;
      .li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        .key {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 23px;
          text-align: center;
          white-space: nowrap;
        }
        .value {
          margin-top: 4px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 24px;
          color: #3fd9ff;
          line-height: 35px;
          text-align: center;
          background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
.divider-h {
  width: 460px;
  height: 1px;
}
.divider-v {
  width: 60px;
  height: 1px;
  transform: rotate(90deg);
}
</style>